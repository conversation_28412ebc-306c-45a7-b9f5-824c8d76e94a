# Core Django Framework
Django==4.2
asgiref==3.8.1  # Required by Django
sqlparse==0.4.4  # Required by Django

# Image Processing
Pillow==11.2.1
pyuploadcare==4.1.0  # Uploadcare image management (updated for pytz compatibility)

# Form Handling
django-crispy-forms==2.3  # For enhanced form rendering (compatible with crispy-bootstrap4 2024.10)
crispy-bootstrap4==2024.10  # Bootstrap 4 template pack for crispy forms

# Database
psycopg2-binary==2.9.9  # PostgreSQL adapter (for production)

# Deployment
gunicorn==21.2.0  # WSGI HTTP Server
whitenoise==6.6.0  # Static file serving

# Environment Variables
python-decouple==3.8  # For managing environment variables

# Security
defusedxml==0.7.1  # Secure XML processing

# Utilities
six==1.16.0  # Python 2 and 3 compatibility utilities
requests==2.31.0  # HTTP library for M-Pesa API integration
pytz==2022.7.1  # Timezone support for Kenya (EAT) - Compatible with pyuploadcare 4.1.0

# Production Dependencies
dj-database-url==2.1.0  # Database URL parsing
