# Generated by Django 4.2 on 2025-06-12 15:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import pyuploadcare.dj.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AutoCreatedAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('initial_password_sent', models.BooleanField(default=False, help_text='Whether the initial password email was sent')),
                ('first_login_token', models.CharField(blank=True, help_text='Token for first-time login from email', max_length=64, null=True, unique=True)),
                ('token_expires', models.DateTimeField(blank=True, help_text='When the first-login token expires', null=True)),
                ('first_login_completed', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Whether user has completed first login')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('slug', models.SlugField(max_length=100, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Coupon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True)),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed', 'Fixed Amount')], default='percentage', max_length=10)),
                ('discount_value', models.DecimalField(decimal_places=2, help_text='For percentage type: enter percentage value (e.g., 10 for 10%). For fixed amount type: enter amount in Kenyan Shillings (KES)', max_digits=10)),
                ('valid_from', models.DateTimeField()),
                ('valid_to', models.DateTimeField()),
                ('min_order_amount', models.DecimalField(decimal_places=2, default=0, help_text='Minimum order amount in Kenyan Shillings (KES)', max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('usage_limit', models.PositiveIntegerField(default=1, help_text='Maximum number of times this coupon can be used')),
                ('usage_count', models.PositiveIntegerField(default=0, help_text='Number of times this coupon has been used')),
                ('per_customer_limit', models.PositiveIntegerField(default=1, help_text='Maximum number of times a customer can use this coupon')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='Ingredient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(max_length=20)),
                ('address', models.CharField(max_length=250)),
                ('area', models.CharField(blank=True, help_text='Area/Neighborhood', max_length=100)),
                ('estate', models.CharField(blank=True, help_text='Estate/Community name', max_length=100)),
                ('building', models.CharField(blank=True, help_text='Apartment/Building/House number', max_length=100)),
                ('landmark', models.CharField(blank=True, help_text='Nearby recognizable location', max_length=150)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('county', models.CharField(blank=True, max_length=100, null=True)),
                ('auto_created_account', models.BooleanField(default=False, help_text='Whether an account was automatically created for this order')),
                ('account_creation_email_sent', models.BooleanField(default=False, help_text='Whether the account creation email was sent')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('payment_method', models.CharField(choices=[('mpesa', 'M-Pesa'), ('card', 'Credit/Debit Card'), ('bank', 'Bank Transfer')], default='mpesa', max_length=20)),
                ('mpesa_phone', models.CharField(blank=True, max_length=20)),
                ('transaction_id', models.CharField(blank=True, max_length=100)),
                ('mpesa_checkout_request_id', models.CharField(blank=True, help_text='M-Pesa STK Push Checkout Request ID', max_length=100)),
                ('mpesa_merchant_request_id', models.CharField(blank=True, help_text='M-Pesa Merchant Request ID', max_length=100)),
                ('mpesa_receipt_number', models.CharField(blank=True, help_text='M-Pesa Receipt Number', max_length=100)),
                ('mpesa_transaction_date', models.DateTimeField(blank=True, help_text='M-Pesa Transaction Date', null=True)),
                ('order_notes', models.TextField(blank=True)),
                ('total_amount', models.DecimalField(decimal_places=2, help_text='Total amount in Kenyan Shillings (KES)', max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, help_text='Discount amount in Kenyan Shillings (KES)', max_digits=10)),
                ('subtotal_amount', models.DecimalField(decimal_places=2, default=0, help_text='Subtotal amount in Kenyan Shillings (KES)', max_digits=10)),
                ('coupon', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders', to='yummytummy_store.coupon')),
                ('user', models.ForeignKey(blank=True, help_text='User account (if account exists)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, help_text='Price in Kenyan Shillings (KES)', max_digits=10)),
                ('size', models.CharField(blank=True, max_length=50)),
                ('image', pyuploadcare.dj.models.ImageField(blank=True, help_text='Upload product image')),
                ('legacy_image', models.ImageField(blank=True, editable=False, null=True, upload_to='products/%Y/%m/%d')),
                ('slug', models.SlugField(max_length=200, unique=True)),
                ('is_available', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('feature_type', models.CharField(blank=True, choices=[('bestseller', 'Bestseller'), ('seasonal', 'Seasonal'), ('limited_time', 'Limited Time'), ('new', 'New Arrival'), ('sale', 'On Sale')], max_length=20)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='yummytummy_store.category')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('additional_price', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='yummytummy_store.product')),
            ],
        ),
        migrations.CreateModel(
            name='ProductIngredient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('ingredient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='yummytummy_store.ingredient')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ingredients', to='yummytummy_store.product')),
            ],
        ),
        migrations.CreateModel(
            name='OrderTrackingStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('order_received', 'Order Received'), ('payment_confirmed', 'Payment Confirmed'), ('processing', 'Processing'), ('packaging', 'Packaging'), ('shipped', 'Shipped'), ('out_for_delivery', 'Out for Delivery'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], max_length=20)),
                ('message', models.TextField(blank=True, help_text='Additional details about this status update')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, help_text='Admin user who created this status update', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tracking_statuses', to='yummytummy_store.order')),
            ],
            options={
                'verbose_name_plural': 'Order Tracking Statuses',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price', models.DecimalField(decimal_places=2, help_text='Price in Kenyan Shillings (KES)', max_digits=10)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='yummytummy_store.order')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='yummytummy_store.product')),
                ('variant', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='yummytummy_store.productvariant')),
            ],
        ),
        migrations.CreateModel(
            name='CouponUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('used_at', models.DateTimeField(auto_now_add=True)),
                ('discount_amount', models.DecimalField(decimal_places=2, help_text='Discount amount in Kenyan Shillings (KES)', max_digits=10)),
                ('coupon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usages', to='yummytummy_store.coupon')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='coupon_usages', to='yummytummy_store.order')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='coupon_usages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-used_at'],
            },
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['code'], name='yummytummy__code_72d833_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['valid_from', 'valid_to'], name='yummytummy__valid_f_db001b_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['is_active'], name='yummytummy__is_acti_07e166_idx'),
        ),
        migrations.AddField(
            model_name='autocreatedaccount',
            name='created_during_order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='auto_account_record', to='yummytummy_store.order'),
        ),
        migrations.AddField(
            model_name='autocreatedaccount',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='auto_created_account', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['id', 'slug'], name='yummytummy__id_07ad7f_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['name'], name='yummytummy__name_162fe8_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['-created'], name='yummytummy__created_d3e9cf_idx'),
        ),
        migrations.AddIndex(
            model_name='ordertrackingstatus',
            index=models.Index(fields=['order', '-created_at'], name='yummytummy__order_i_fe5752_idx'),
        ),
        migrations.AddIndex(
            model_name='ordertrackingstatus',
            index=models.Index(fields=['status'], name='yummytummy__status_cd71a0_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['-created'], name='yummytummy__created_5f751c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='couponusage',
            unique_together={('coupon', 'order')},
        ),
        migrations.AddIndex(
            model_name='autocreatedaccount',
            index=models.Index(fields=['first_login_token'], name='yummytummy__first_l_9f0406_idx'),
        ),
        migrations.AddIndex(
            model_name='autocreatedaccount',
            index=models.Index(fields=['token_expires'], name='yummytummy__token_e_ed942d_idx'),
        ),
        migrations.AddIndex(
            model_name='autocreatedaccount',
            index=models.Index(fields=['-created_at'], name='yummytummy__created_ec13a3_idx'),
        ),
    ]
