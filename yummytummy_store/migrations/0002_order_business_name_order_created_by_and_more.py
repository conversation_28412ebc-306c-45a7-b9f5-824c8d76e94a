# Generated by Django 4.2 on 2025-06-13 19:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('yummytummy_store', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='business_name',
            field=models.CharField(blank=True, help_text='Business name for business customers', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='created_by',
            field=models.ForeignKey(blank=True, help_text='Sales person who created this offline order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_orders', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='order',
            name='customer_type',
            field=models.CharField(choices=[('individual', 'Individual Customer'), ('business', 'Business Customer')], default='individual', help_text='Type of customer for this order', max_length=20),
        ),
        migrations.AlterField(
            model_name='ordertrackingstatus',
            name='status',
            field=models.CharField(choices=[('order_received', 'Order Received'), ('payment_confirmed', 'Payment Confirmed'), ('offline_order_created', 'Offline Order Created'), ('processing', 'Processing'), ('packaging', 'Packaging'), ('shipped', 'Shipped'), ('out_for_delivery', 'Out for Delivery'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], max_length=25),
        ),
    ]
