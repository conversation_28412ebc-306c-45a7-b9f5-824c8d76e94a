{% load tz %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Offline Order - {{ order.get_order_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #593500;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f5f2ed;
            padding: 20px;
            border-radius: 0 0 8px 8px;
        }
        .order-info {
            background-color: white;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            border-left: 4px solid #593500;
        }
        .customer-info {
            background-color: white;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background-color: white;
        }
        .items-table th,
        .items-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .items-table th {
            background-color: #593500;
            color: white;
        }
        .total-row {
            font-weight: bold;
            background-color: #f5f2ed;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background-color: #593500;
            color: white;
            border-radius: 6px;
        }
        .alert {
            background-color: #ffc107;
            color: #000;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        @media (max-width: 600px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛒 New Offline Order Created</h1>
        <p>Order #{{ order.get_order_number }}</p>
    </div>
    
    <div class="content">
        <div class="alert">
            <strong>⚠️ Action Required:</strong> A new offline order has been created by the sales team and requires processing.
        </div>
        
        <div class="order-info">
            <h3>📋 Order Details</h3>
            <p><strong>Order Number:</strong> {{ order.get_order_number }}</p>
            <p><strong>Created By:</strong> {{ sales_person.get_full_name|default:sales_person.username }} ({{ sales_person.email }})</p>
            <p><strong>Order Date:</strong> {% timezone "Africa/Nairobi" %}{{ order.created|date:"F d, Y \a\t H:i" }} EAT{% endtimezone %}</p>
            <p><strong>Customer Type:</strong> 
                {% if order.customer_type == 'business' %}
                    🏢 Business Customer
                {% else %}
                    👤 Individual Customer
                {% endif %}
            </p>
            <p><strong>Payment Method:</strong> Offline Order (Payment to be arranged)</p>
            <p><strong>Order Status:</strong> Offline Order Created</p>
        </div>
        
        <div class="info-grid">
            <div class="customer-info">
                <h3>👤 Customer Information</h3>
                <p><strong>Name:</strong> {{ order.first_name }} {{ order.last_name }}</p>
                {% if order.business_name %}
                <p><strong>Business:</strong> {{ order.business_name }}</p>
                {% endif %}
                <p><strong>Email:</strong> {{ order.email }}</p>
                <p><strong>Phone:</strong> {{ order.phone }}</p>
            </div>
            
            <div class="customer-info">
                <h3>🚚 Delivery Information</h3>
                <p><strong>Address:</strong> {{ order.address }}</p>
                <p><strong>City:</strong> {{ order.city }}</p>
                <p><strong>County:</strong> {{ order.county }}</p>
            </div>
        </div>
        
        <h3>🛍️ Order Items</h3>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Size</th>
                    <th>Qty</th>
                    <th>Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order_items %}
                <tr>
                    <td>{{ item.product.name }}</td>
                    <td>
                        {% if item.variant %}
                            {{ item.variant.size }}
                        {% else %}
                            Standard
                        {% endif %}
                    </td>
                    <td>{{ item.quantity }}</td>
                    <td>KSh {{ item.price }}</td>
                    <td>KSh {{ item.get_cost }}</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr class="total-row">
                    <td colspan="4"><strong>Total Amount:</strong></td>
                    <td><strong>KSh {{ order.total_amount }}</strong></td>
                </tr>
            </tfoot>
        </table>
        
        {% if order.order_notes %}
        <div class="order-info">
            <h3>📝 Order Notes</h3>
            <p>{{ order.order_notes }}</p>
        </div>
        {% endif %}
        
        <div class="order-info">
            <h3>📧 Customer Notification</h3>
            <p>✅ Order confirmation email has been automatically sent to the customer at <strong>{{ order.email }}</strong></p>
            <p>The customer will receive status updates as the order progresses through the fulfillment process.</p>
        </div>
        
        <div class="alert">
            <h4>🎯 Next Steps:</h4>
            <ol>
                <li><strong>Review Order:</strong> Verify all order details and customer information</li>
                <li><strong>Arrange Payment:</strong> Contact customer to arrange payment method</li>
                <li><strong>Update Status:</strong> Use the admin dashboard to update order status as it progresses</li>
                <li><strong>Process Order:</strong> Begin order fulfillment once payment is confirmed</li>
            </ol>
        </div>
    </div>
    
    <div class="footer">
        <h3>🏪 YummyTummy Business</h3>
        <p>Offline Order Management System</p>
        <p>
            <strong>Time:</strong> {% timezone "Africa/Nairobi" %}{{ current_time|date:"F d, Y \a\t H:i" }} EAT{% endtimezone %}
        </p>
        <p><em>This is an automated notification from the YummyTummy offline order system.</em></p>
    </div>
</body>
</html>
