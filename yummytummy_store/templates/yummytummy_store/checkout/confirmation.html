{% extends "yummytummy_store/base.html" %}
{% load static %}
{% load humanize %}
{% load tz %}

{% block title %}Order Confirmation | YummyTummy Shop{% endblock %}

{% block extra_css %}
<style>
    .confirmation-section {
        padding: 60px 0;
    }

    .checkout-steps {
        display: flex;
        margin-bottom: 30px;
        border-bottom: 1px solid var(--light-gray);
        padding-bottom: 20px;
    }

    .checkout-step {
        flex: 1;
        text-align: center;
        position: relative;
        padding-bottom: 10px;
    }

    .checkout-step.active {
        color: var(--primary-color);
        font-weight: 700;
    }

    .checkout-step.completed {
        color: var(--dark-gray);
    }

    .checkout-step.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 3px;
        background-color: var(--yellow);
    }

    .checkout-step.completed::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 3px;
        background-color: var(--light-gray);
    }

    .confirmation-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .confirmation-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .confirmation-icon {
        font-size: 4rem;
        color: #51cf66;
        margin-bottom: 20px;
    }

    .confirmation-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
        color: var(--primary-color);
    }

    .confirmation-subtitle {
        font-size: 1.1rem;
        color: var(--dark-gray);
        margin-bottom: 20px;
    }

    .order-number-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
        margin-top: 20px;
    }

    .order-number {
        font-size: 1.2rem;
        font-weight: 600;
        background-color: var(--light-gray);
        padding: 10px 20px;
        border-radius: 5px;
        display: inline-block;
    }

    .track-order-btn-inline {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 10px 20px;
        border-radius: 5px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .track-order-btn-inline:hover {
        background-color: #4a2a00;
        color: var(--secondary-color);
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(89, 53, 0, 0.3);
    }

    .track-order-btn-inline i {
        font-size: 0.9rem;
    }

    .confirmation-details {
        background-color: var(--secondary-color);
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .confirmation-section-title {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 15px 20px;
        font-size: 1.2rem;
        font-weight: 600;
    }

    .confirmation-section-content {
        padding: 20px;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .info-group {
        margin-bottom: 15px;
    }

    .info-label {
        font-weight: 600;
        margin-bottom: 5px;
        color: var(--primary-color);
    }

    .info-value {
        color: var(--dark-gray);
    }

    .order-items {
        margin-top: 20px;
    }

    .order-item {
        display: flex;
        justify-content: space-between;
        padding: 15px 0;
        border-bottom: 1px solid var(--light-gray);
    }

    .order-item:last-child {
        border-bottom: none;
    }

    .item-details {
        display: flex;
        align-items: center;
    }

    .item-quantity {
        background-color: var(--light-gray);
        color: var(--primary-color);
        font-weight: 600;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .item-name {
        font-weight: 600;
    }

    .item-price {
        font-weight: 600;
    }

    .order-summary {
        margin-top: 20px;
        border-top: 1px solid var(--light-gray);
        padding-top: 20px;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .summary-row.total {
        font-weight: 700;
        font-size: 1.1rem;
        color: var(--primary-color);
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid var(--light-gray);
    }

    .summary-row.discount {
        color: #28a745;
        font-weight: 600;
    }

    .coupon-applied {
        margin-top: 15px;
        padding-top: 15px;
    }

    .coupon-badge {
        display: inline-block;
        background-color: rgba(255, 193, 7, 0.1);
        border: 1px dashed var(--yellow);
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 0.9rem;
        color: var(--primary-color);
    }

    .payment-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-top: 20px;
    }

    .payment-method {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .payment-icon {
        margin-right: 10px;
        font-size: 1.2rem;
    }

    .next-steps {
        margin-top: 30px;
        text-align: center;
    }

    .next-steps-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: var(--primary-color);
    }

    .next-steps-list {
        text-align: left;
        max-width: 500px;
        margin: 0 auto 30px;
    }

    .next-steps-list li {
        margin-bottom: 10px;
        color: var(--dark-gray);
    }

    .confirmation-actions {
        display: flex;
        gap: 15px;
        margin-top: 20px;
        flex-wrap: wrap;
    }

    .continue-shopping-btn, .track-order-btn {
        display: inline-flex;
        align-items: center;
        gap: 10px;
        padding: 12px 25px;
        border-radius: 5px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s;
        flex: 1;
        justify-content: center;
        min-width: 200px;
    }

    .continue-shopping-btn {
        background-color: var(--yellow);
        color: var(--primary-color);
    }

    .continue-shopping-btn:hover {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        transform: translateY(-2px);
    }

    .track-order-btn {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        border: 2px solid var(--primary-color);
    }

    .track-order-btn:hover {
        background-color: var(--yellow);
        color: var(--primary-color);
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .confirmation-actions {
            flex-direction: column;
        }

        .continue-shopping-btn, .track-order-btn {
            min-width: auto;
        }
    }

    @media (max-width: 768px) {
        .info-grid {
            grid-template-columns: 1fr;
        }
    }

    /* M-Pesa Status Styles */
    .payment-info {
        margin-top: 20px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid var(--yellow);
    }

    .payment-method {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        font-weight: 600;
        color: var(--primary-color);
    }

    .payment-icon {
        font-size: 1.2rem;
        color: var(--yellow);
    }

    .mpesa-success, .mpesa-pending, .mpesa-failed {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 15px;
        border-radius: 6px;
        margin-top: 15px;
    }

    .mpesa-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .mpesa-pending {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }

    .mpesa-failed {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .success-icon {
        color: #28a745;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .pending-icon {
        color: #ffc107;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .failed-icon {
        color: #dc3545;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    /* What Happens Next Section Styles */
    .what-happens-next {
        background-color: var(--highlight-color);
        border: 1px solid rgba(89, 53, 0, 0.1);
        border-radius: 8px;
        padding: 25px;
        margin: 30px 0;
        text-align: center;
    }

    .what-happens-next-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .what-happens-next-content {
        text-align: left;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
        color: var(--dark-gray);
    }

    .what-happens-next-content p {
        margin-bottom: 12px;
        padding-left: 20px;
        position: relative;
    }

    .what-happens-next-content p:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #28a745;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .what-happens-next-content p:last-child {
        margin-bottom: 0;
        font-weight: 600;
        color: var(--primary-color);
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .order-number-container {
            flex-direction: column;
            gap: 10px;
        }

        .order-number {
            font-size: 1rem;
            padding: 8px 16px;
        }

        .track-order-btn-inline {
            font-size: 0.9rem;
            padding: 8px 16px;
        }

        .confirmation-title {
            font-size: 1.5rem;
        }

        .confirmation-subtitle {
            font-size: 1rem;
        }

        .what-happens-next {
            padding: 20px 15px;
            margin: 20px 0;
        }

        .what-happens-next-title {
            font-size: 1.1rem;
            flex-direction: column;
            gap: 5px;
        }

        .what-happens-next-content {
            font-size: 0.95rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="confirmation-section">
    <div class="container">
        <div class="checkout-steps">
            <div class="checkout-step completed">1. Delivery</div>
            <div class="checkout-step completed">2. Payment</div>
            <div class="checkout-step active">3. Confirmation</div>
        </div>

        <div class="confirmation-container">
            <div class="confirmation-header">
                <div class="confirmation-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h1 class="confirmation-title">Thank You for Your Order!</h1>
                <p class="confirmation-subtitle">Your order has been received and is now being processed. You can track the status of your order below.</p>
                <div class="order-number-container">
                    <div class="order-number">Order #{{ order.get_order_number }}</div>
                    {% if user.is_authenticated %}
                    <a href="{% url 'yummytummy_store:order_tracking_dashboard' %}" class="track-order-btn-inline">
                        <i class="fas fa-search"></i> Track Your Order
                    </a>
                    {% else %}
                    <a href="{% url 'yummytummy_store:guest_order_tracking' %}" class="track-order-btn-inline">
                        <i class="fas fa-search"></i> Track Your Order
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- What Happens Next Section -->
            <div class="what-happens-next">
                <h2 class="what-happens-next-title">
                    <i class="fas fa-info-circle"></i>
                    What Happens Next?
                </h2>
                <div class="what-happens-next-content">
                    <p>You will receive an order confirmation email with tracking information.</p>
                    <p>Once your payment is confirmed, we will prepare your order for shipping.</p>
                    <p>You will receive a shipping notification when your order is on its way.</p>
                    <p>Your delicious YummyTummy peanut butter will arrive at your doorstep soon!</p>
                </div>
            </div>

            <div class="confirmation-details">
                <div class="confirmation-section-title">Order Details</div>
                <div class="confirmation-section-content">
                    <div class="info-grid">
                        <div>
                            <div class="info-group">
                                <div class="info-label">Order Date</div>
                                <div class="info-value">{{ order.created|date:"F j, Y" }}</div>
                            </div>
                            <div class="info-group">
                                <div class="info-label">Payment Status</div>
                                <div class="info-value">{{ order.get_payment_status_display }}</div>
                            </div>
                        </div>
                        <div>
                            <div class="info-group">
                                <div class="info-label">Payment Method</div>
                                <div class="info-value">{{ order.get_payment_method_display }}</div>
                            </div>
                            {% if order.transaction_id %}
                            <div class="info-group">
                                <div class="info-label">Transaction ID</div>
                                <div class="info-value">{{ order.transaction_id }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="order-items">
                        {% for item in order_items %}
                        <div class="order-item">
                            <div class="item-details">
                                <div class="item-quantity">{{ item.quantity }}</div>
                                <div class="item-name">{{ item.product.name }}</div>
                            </div>
                            <div class="item-price">KSh {{ item.price|floatformat:"2"|intcomma }}</div>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="order-summary">
                        <div class="summary-row">
                            <div>Subtotal</div>
                            <div>KSh {{ order.subtotal_amount|floatformat:"2"|intcomma }}</div>
                        </div>

                        {% if order.discount_amount > 0 %}
                        <div class="summary-row discount">
                            <div>Discount</div>
                            <div>-KSh {{ order.discount_amount|floatformat:"2"|intcomma }}</div>
                        </div>
                        {% endif %}

                        <div class="summary-row">
                            <div>Shipping</div>
                            <div>Free</div>
                        </div>
                        <div class="summary-row total">
                            <div>Total</div>
                            <div>KSh {{ order.total_amount|floatformat:"2"|intcomma }}</div>
                        </div>

                        {% if order.coupon %}
                        <div class="coupon-applied">
                            <div class="coupon-badge">
                                <i class="fas fa-tag"></i> Coupon Applied: {{ order.coupon.code }}
                                {% if order.coupon.discount_type == 'percentage' %}
                                ({{ order.coupon.discount_value }}% OFF)
                                {% else %}
                                (KSh {{ order.coupon.discount_value|floatformat:"2"|intcomma }} OFF)
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    {% if order.payment_method == 'mpesa' %}
                    <div class="payment-info">
                        <div class="payment-method">
                            <div class="payment-icon"><i class="fas fa-mobile-alt"></i></div>
                            <div>M-Pesa payment to phone number: {{ order.mpesa_phone }}</div>
                        </div>

                        {% if order.payment_status == 'completed' and order.mpesa_receipt_number %}
                        <div class="mpesa-success">
                            <div class="success-icon"><i class="fas fa-check-circle"></i></div>
                            <div>
                                <strong>Payment Successful!</strong><br>
                                M-Pesa Receipt: {{ order.mpesa_receipt_number }}<br>
                                {% if order.mpesa_transaction_date %}
                                Transaction Date: {% timezone "Africa/Nairobi" %}{{ order.mpesa_transaction_date|date:"M d, Y H:i" }} EAT{% endtimezone %}
                                {% endif %}
                            </div>
                        </div>
                        {% elif order.payment_status == 'processing' %}
                        <div class="mpesa-pending">
                            <div class="pending-icon"><i class="fas fa-clock"></i></div>
                            <div>
                                <strong>Payment Pending</strong><br>
                                A payment prompt has been sent to your phone. Please complete the transaction by entering your M-Pesa PIN when prompted.
                            </div>
                        </div>
                        {% elif order.payment_status == 'failed' %}
                        <div class="mpesa-failed">
                            <div class="failed-icon"><i class="fas fa-exclamation-triangle"></i></div>
                            <div>
                                <strong>Payment Failed</strong><br>
                                Please contact our support team for assistance with your payment.
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="confirmation-details">
                <div class="confirmation-section-title">Customer Information</div>
                <div class="confirmation-section-content">
                    <div class="info-grid">
                        <div>
                            <div class="info-group">
                                <div class="info-label">Contact Information</div>
                                <div class="info-value">{{ order.first_name }} {{ order.last_name }}</div>
                                <div class="info-value">{{ order.email }}</div>
                                <div class="info-value">{{ order.phone }}</div>
                            </div>
                        </div>
                        <div>
                            <div class="info-group">
                                <div class="info-label">Delivery Address</div>
                                <div class="info-value">{{ order.address }}</div>
                                {% if order.area %}
                                <div class="info-value">{{ order.area }}{% if order.estate %}, {{ order.estate }}{% endif %}</div>
                                {% endif %}
                                {% if order.building %}
                                <div class="info-value">{{ order.building }}</div>
                                {% endif %}
                                {% if order.landmark %}
                                <div class="info-value">Near: {{ order.landmark }}</div>
                                {% endif %}

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="next-steps">
                <h3 class="next-steps-title">What Happens Next?</h3>
                <ol class="next-steps-list">
                    <li>You will receive an order confirmation email with tracking information.</li>
                    <li>Once your payment is confirmed, we will prepare your order for shipping.</li>
                    <li>You will receive a shipping notification when your order is on its way.</li>
                    <li>Your delicious YummyTummy peanut butter will arrive at your doorstep soon!</li>
                </ol>

                <div class="tracking-options" style="background-color: var(--highlight-color); padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h4 style="margin-bottom: 15px; color: var(--primary-color);">
                        <i class="fas fa-info-circle"></i> Track Your Order
                    </h4>
                    <p style="margin-bottom: 15px; color: var(--dark-gray);">
                        You can track your order progress in two ways:
                    </p>
                    <ul style="margin-bottom: 15px; color: var(--dark-gray);">
                        <li><strong>Guest Tracking:</strong> Use your order number and email address</li>
                        <li><strong>Account Access:</strong> Log in to view all your orders and history</li>
                    </ul>
                    <p style="font-size: 0.9rem; color: #666;">
                        <em>We've sent you an email with login details for easy access to your order history.</em>
                    </p>
                </div>

                <div class="confirmation-actions">
                    {% if user.is_authenticated %}
                    <a href="{% url 'yummytummy_store:order_tracking_dashboard' %}" class="track-order-btn">
                        <i class="fas fa-box"></i> View Your Orders
                    </a>
                    {% else %}
                    <a href="{% url 'yummytummy_store:guest_order_tracking' %}" class="track-order-btn">
                        <i class="fas fa-search"></i> Track This Order
                    </a>
                    {% endif %}
                    <a href="{% url 'yummytummy_store:product_list' %}" class="continue-shopping-btn">
                        <i class="fas fa-arrow-left"></i> Return to Shop
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
