{% extends "yummytummy_store/base.html" %}
{% load static %}
{% load humanize %}

{% block title %}Checkout - Shipping | YummyTummy Shop{% endblock %}

{% block extra_css %}
<style>
    .checkout-section {
        padding: 60px 0;
    }

    .checkout-container {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
    }

    .checkout-form-container {
        flex: 1;
        min-width: 300px;
    }

    .order-summary-container {
        width: 350px;
    }

    .checkout-steps {
        display: flex;
        margin-bottom: 30px;
        border-bottom: 1px solid var(--light-gray);
        padding-bottom: 20px;
    }

    .checkout-step {
        flex: 1;
        text-align: center;
        position: relative;
        padding-bottom: 10px;
    }

    .checkout-step.active {
        color: var(--primary-color);
        font-weight: 700;
    }

    .checkout-step.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 3px;
        background-color: var(--yellow);
    }

    .checkout-form {
        background-color: var(--secondary-color);
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .form-section {
        margin-bottom: 30px;
    }

    .form-section-title {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: var(--primary-color);
        border-bottom: 1px solid var(--light-gray);
        padding-bottom: 10px;
    }

    .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }

    .form-group {
        flex: 1;
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: var(--primary-color);
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid var(--light-gray);
        border-radius: 5px;
        font-size: 1rem;
        transition: border-color 0.3s;
    }

    .form-control:focus {
        border-color: var(--yellow);
        outline: none;
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
    }

    .form-control.is-invalid {
        border-color: #dc3545;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 5px;
    }

    .order-summary {
        background-color: var(--secondary-color);
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .order-summary-title {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: var(--primary-color);
        border-bottom: 1px solid var(--light-gray);
        padding-bottom: 10px;
    }

    .order-items {
        margin-bottom: 20px;
    }

    .order-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px dashed var(--light-gray);
    }

    .order-item:last-child {
        border-bottom: none;
    }

    .item-details {
        flex: 1;
    }

    .item-name {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .item-quantity {
        color: var(--dark-gray);
        font-size: 0.9rem;
    }

    .item-price {
        font-weight: 600;
        text-align: right;
    }

    .order-totals {
        border-top: 1px solid var(--light-gray);
        padding-top: 15px;
    }

    .order-total-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .order-total-row.final {
        font-weight: 700;
        font-size: 1.1rem;
        color: var(--primary-color);
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid var(--light-gray);
    }

    .order-total-row.discount {
        color: #28a745;
        font-weight: 600;
    }

    .applied-coupon-info {
        margin-top: 15px;
        padding-top: 15px;
    }

    .coupon-badge {
        display: inline-block;
        background-color: rgba(255, 193, 7, 0.1);
        border: 1px dashed var(--yellow);
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 0.9rem;
        color: var(--primary-color);
    }

    .checkout-button {
        display: block;
        width: 100%;
        padding: 15px;
        background-color: var(--yellow);
        color: var(--primary-color);
        border: none;
        border-radius: 5px;
        font-size: 1rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s;
        text-align: center;
        margin-top: 20px;
    }

    .checkout-button:hover {
        background-color: var(--primary-color);
        color: var(--secondary-color);
    }

    @media (max-width: 768px) {
        .checkout-container {
            flex-direction: column;
        }

        .order-summary-container {
            width: 100%;
        }

        .form-row {
            flex-direction: column;
            gap: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="checkout-section">
    <div class="container">
        <h1 class="section-title">Checkout</h1>

        <div class="checkout-steps">
            <div class="checkout-step active">1. Delivery</div>
            <div class="checkout-step">2. Payment</div>
            <div class="checkout-step">3. Confirmation</div>
        </div>

        <div class="checkout-container">
            <div class="checkout-form-container">
                <form method="post" class="checkout-form">
                    {% csrf_token %}

                    <div class="form-section">
                        <h3 class="form-section-title">Contact Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="{{ form.first_name.id_for_label }}">First Name</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                <div class="invalid-feedback">
                                    {{ form.first_name.errors }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.last_name.id_for_label }}">Last Name</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                <div class="invalid-feedback">
                                    {{ form.last_name.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}">Email Address</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {{ form.email.errors }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.phone.id_for_label }}">Phone Number</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {{ form.phone.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">Delivery Address</h3>
                        <div class="form-group">
                            <label for="{{ form.address.id_for_label }}">Street Address</label>
                            {{ form.address }}
                            {% if form.address.errors %}
                            <div class="invalid-feedback">
                                {{ form.address.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="{{ form.area.id_for_label }}">Area/Neighborhood</label>
                                {{ form.area }}
                                {% if form.area.errors %}
                                <div class="invalid-feedback">
                                    {{ form.area.errors }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.estate.id_for_label }}">Estate/Community Name</label>
                                {{ form.estate }}
                                {% if form.estate.errors %}
                                <div class="invalid-feedback">
                                    {{ form.estate.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="{{ form.building.id_for_label }}">Apartment/Building/House Number</label>
                                {{ form.building }}
                                {% if form.building.errors %}
                                <div class="invalid-feedback">
                                    {{ form.building.errors }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.landmark.id_for_label }}">Nearby Recognizable Location</label>
                                {{ form.landmark }}
                                {% if form.landmark.errors %}
                                <div class="invalid-feedback">
                                    {{ form.landmark.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>


                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">Order Notes</h3>
                        <div class="form-group">
                            <label for="{{ form.order_notes.id_for_label }}">Additional Notes (Optional)</label>
                            {{ form.order_notes }}
                        </div>
                    </div>

                    <button type="submit" class="checkout-button">Continue to Payment</button>
                </form>
            </div>

            <div class="order-summary-container">
                <div class="order-summary">
                    <h3 class="order-summary-title">Order Summary</h3>

                    <div class="order-items">
                        {% for item in cart_items %}
                        <div class="order-item">
                            <div class="item-details">
                                <div class="item-name">{{ item.name }}</div>
                                <div class="item-quantity">Quantity: {{ item.quantity }}</div>
                            </div>
                            <div class="item-price">KSh {{ item.subtotal|floatformat:"2"|intcomma }}</div>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="order-totals">
                        <div class="order-total-row">
                            <div>Subtotal</div>
                            <div>KSh {{ subtotal|floatformat:"2"|intcomma }}</div>
                        </div>

                        {% if discount > 0 %}
                        <div class="order-total-row discount">
                            <div>Discount</div>
                            <div>-KSh {{ discount|floatformat:"2"|intcomma }}</div>
                        </div>
                        {% endif %}

                        <div class="order-total-row">
                            <div>Shipping</div>
                            <div>Free</div>
                        </div>
                        <div class="order-total-row final">
                            <div>Total</div>
                            <div>KSh {{ total|floatformat:"2"|intcomma }}</div>
                        </div>

                        {% if coupon %}
                        <div class="applied-coupon-info">
                            <div class="coupon-badge">
                                <i class="fas fa-tag"></i> Coupon Applied: {{ coupon.code }}
                                {% if coupon.discount_type == 'percentage' %}
                                ({{ coupon.discount_value }}% OFF)
                                {% else %}
                                (KSh {{ coupon.discount_value|floatformat:"2"|intcomma }} OFF)
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
