{% load static %}
{% load humanize %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Peanut Butter 100% Nuts | YummyTummy Shop{% endblock %}</title>
    <link rel="stylesheet" href="{% static 'yummytummy_store/css/normalize.css' %}">
    <link rel="stylesheet" href="{% static 'yummytummy_store/css/styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Account Dropdown Styles -->
    <style>
        /* Account Dropdown Styles */
        .account-dropdown {
            position: relative;
        }

        .account-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .account-toggle:hover {
            color: var(--yellow);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--secondary-color);
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            margin-top: 10px;
        }

        .account-dropdown:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid var(--light-gray);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background-color: var(--cream);
            color: var(--primary-color);
        }

        .dropdown-item.logout:hover {
            background-color: #f8d7da;
            color: #721c24;
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--light-gray);
            margin: 5px 0;
        }

        .mobile-account-section {
            border-top: 1px solid var(--light-gray);
            margin-top: 10px;
            padding-top: 10px;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .dropdown-menu {
                position: static;
                opacity: 1;
                visibility: visible;
                transform: none;
                box-shadow: none;
                border: none;
                background: transparent;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="top-header">
            <div class="container">
                <nav class="top-nav">
                    <ul class="main-menu">
                        <li><a href="{% url 'yummytummy_store:home' %}">HOME</a></li>
                        <li><a href="{% url 'yummytummy_store:about' %}">ABOUT</a></li>
                        <li><a href="{% url 'yummytummy_store:product_list' %}">SHOP</a></li>
                        <li><a href="{% url 'yummytummy_store:guest_order_tracking' %}">TRACK ORDER</a></li>
                        <li><a href="#">BLOG</a></li>
                        <li><a href="#">RECIPES</a></li>
                    </ul>
                    <a href="{% url 'yummytummy_store:home' %}" class="logo">
                        <img src="{% static 'yummytummy_store/img/logo.svg' %}" alt="Logo YummyTummy">
                    </a>
                    <ul class="secondary-menu">
                        <li><a href="#">BUSINESS COOPERATION</a></li>
                        <li><a href="#"><img src="{% static 'yummytummy_store/img/Flag_of_Kenya.svg' %}" alt="Kenyan Flag" class="flag-icon"></a></li>
                        <li class="account-dropdown">
                            {% if user.is_authenticated %}
                                <a href="#" class="account-toggle">
                                    <i class="fas fa-user"></i>
                                    {{ user.first_name|default:user.username }}
                                    <i class="fas fa-chevron-down"></i>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="{% url 'yummytummy_store:account_profile' %}" class="dropdown-item">
                                        <i class="fas fa-user-circle"></i>
                                        My Profile
                                    </a>
                                    <a href="{% url 'yummytummy_store:order_tracking_dashboard' %}" class="dropdown-item">
                                        <i class="fas fa-box"></i>
                                        My Orders
                                    </a>
                                    <a href="{% url 'password_change' %}" class="dropdown-item">
                                        <i class="fas fa-key"></i>
                                        Change Password
                                    </a>
                                    {% if user.is_staff %}
                                    <div class="dropdown-divider"></div>
                                    <a href="{% url 'yummytummy_store:admin_dashboard' %}" class="dropdown-item">
                                        <i class="fas fa-chart-line"></i>
                                        Admin Dashboard
                                    </a>
                                    <a href="{% url 'yummytummy_store:how_it_works' %}" class="dropdown-item">
                                        <i class="fas fa-cogs"></i>
                                        How It Works
                                    </a>
                                    <a href="/admin/" class="dropdown-item">
                                        <i class="fas fa-tools"></i>
                                        Django Admin
                                    </a>
                                    {% endif %}
                                    <div class="dropdown-divider"></div>
                                    <a href="{% url 'logout' %}" class="dropdown-item logout">
                                        <i class="fas fa-sign-out-alt"></i>
                                        Logout
                                    </a>
                                </div>
                            {% else %}
                                <a href="{% url 'login' %}">
                                    <i class="fas fa-user"></i>
                                    MY ACCOUNT
                                </a>
                            {% endif %}
                        </li>
                        <li class="cart-icon">
                            <a href="{% url 'yummytummy_store:cart_detail' %}">
                                <span class="cart-count">{{ cart_items_count }}</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        <div class="mobile-header">
            <a href="{% url 'yummytummy_store:home' %}" class="logo">
                <img src="{% static 'yummytummy_store/img/logo.svg' %}" alt="Logo YummyTummy">
            </a>
            <div class="mobile-icons">
                {% if user.is_authenticated %}
                    <a href="{% url 'yummytummy_store:account_profile' %}" class="user-icon">
                        <i class="fas fa-user"></i>
                    </a>
                {% else %}
                    <a href="{% url 'login' %}" class="user-icon">
                        <i class="fas fa-user"></i>
                    </a>
                {% endif %}
                <a href="{% url 'yummytummy_store:cart_detail' %}" class="cart-icon"><i class="fas fa-shopping-cart"></i></a>
                <button class="menu-toggle" aria-label="Toggle Menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            <nav class="mobile-nav">
                <ul>
                    <li><a href="{% url 'yummytummy_store:home' %}">HOME</a></li>
                    <li><a href="{% url 'yummytummy_store:about' %}">ABOUT</a></li>
                    <li><a href="{% url 'yummytummy_store:product_list' %}">SHOP</a></li>
                    <li><a href="{% url 'yummytummy_store:guest_order_tracking' %}">TRACK ORDER</a></li>
                    <li><a href="#">BLOG</a></li>
                    <li><a href="#">RECIPES</a></li>
                    <li><a href="#">BUSINESS COOPERATION</a></li>
                    <li><a href="{% url 'yummytummy_store:contact' %}">CONTACT</a></li>
                    {% if user.is_authenticated %}
                        <li class="mobile-account-section">
                            <a href="{% url 'yummytummy_store:account_profile' %}">
                                <i class="fas fa-user-circle"></i> My Profile
                            </a>
                        </li>
                        <li>
                            <a href="{% url 'yummytummy_store:order_tracking_dashboard' %}">
                                <i class="fas fa-box"></i> My Orders
                            </a>
                        </li>
                        <li>
                            <a href="{% url 'logout' %}">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    {% else %}
                        <li>
                            <a href="{% url 'login' %}">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </header>

    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
        {% for message in messages %}
        <div class="message {{ message.tags }}">
            {{ message }}
            <button class="close-message">&times;</button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    {% block content %}{% endblock %}

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="footer-top">
                <div class="social-links">
                    <a href="#" class="social-link"><img src="{% static 'yummytummy_store/img/facebook-icon.svg' %}" alt="Facebook"></a>
                    <a href="{% url 'yummytummy_store:home' %}" class="logo"><img src="{% static 'yummytummy_store/img/logo.svg' %}" alt="YummyTummy"></a>
                    <a href="#" class="social-link"><img src="{% static 'yummytummy_store/img/instagram-icon.svg' %}" alt="Instagram"></a>
                </div>
                <div class="contact-info">
                    <a href="tel:660805522" class="phone">660 805 522</a>
                    <a href="mailto:<EMAIL>" class="email"><EMAIL></a>
                    <address class="address">1 Industrial St, 16-010 Wasilkow</address>
                    <a href="#" class="map-link">view on map</a>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-links">
                    <a href="#" class="kenyan-flag"><img src="{% static 'yummytummy_store/img/Flag_of_Kenya.svg' %}" alt="Kenyan Flag" class="flag-icon"></a>
                    <a href="#">B2B Zone</a>
                </div>
                <div class="copyright">
                    © {% now "Y" %} YummyTummy.
                </div>
                <div class="legal-links">
                    <a href="#">Terms & Conditions</a>
                    <a href="#">Privacy Policy</a>
                    <a href="#">Grants</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="{% static 'yummytummy_store/js/main.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
