{% load static %}
{% load tz %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Offline Orders - YummyTummy{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #593500;
            --secondary-color: #ffffff;
            --accent-color: #f5f2ed;
            --highlight-color: #ffc107;
            --dark-gray: #6c757d;
            --light-gray: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--accent-color);
            color: #333;
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }

        .navbar {
            background-color: var(--secondary-color) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #7a4a00;
            border-color: #7a4a00;
        }

        .btn-warning {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
            color: #000;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .card-header {
            background-color: var(--primary-color);
            color: var(--secondary-color);
            border-radius: 12px 12px 0 0 !important;
            font-weight: 600;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(89, 53, 0, 0.25);
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .footer {
            background-color: var(--primary-color);
            color: var(--secondary-color);
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .product-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: var(--secondary-color);
        }

        .product-item.selected {
            border-color: var(--primary-color);
            background-color: var(--accent-color);
        }

        .order-summary {
            background-color: var(--secondary-color);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .customer-type-radio {
            margin-right: 1rem;
        }

        .business-fields {
            display: none;
        }

        .business-fields.show {
            display: block;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }
            
            .card {
                margin-bottom: 1rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="{% url 'yummytummy_store:offline_orders_dashboard' %}">
                <i class="fas fa-store"></i> YummyTummy - Offline Orders
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'yummytummy_store:offline_orders_dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'yummytummy_store:create_offline_order' %}">
                                <i class="fas fa-plus-circle"></i> Create Order
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'yummytummy_store:offline_orders_list' %}">
                                <i class="fas fa-list"></i> My Orders
                            </a>
                        </li>
                        {% if user.is_superuser %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'yummytummy_store:admin_dashboard' %}">
                                    <i class="fas fa-cog"></i> Admin
                                </a>
                            </li>
                        {% endif %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'admin:logout' %}">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'yummytummy_store:sales_login' %}">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container my-4">
        <!-- Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>YummyTummy Offline Orders</h5>
                    <p>Sales team order management system</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>
                        <i class="fas fa-clock"></i> 
                        Current Time: {% timezone "Africa/Nairobi" %}{{ current_time|date:"M d, Y H:i" }} EAT{% endtimezone %}
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
