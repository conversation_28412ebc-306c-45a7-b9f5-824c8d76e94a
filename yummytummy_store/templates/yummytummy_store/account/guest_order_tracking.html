{% extends "yummytummy_store/base.html" %}
{% load static %}
{% load humanize %}
{% load tz %}

{% block title %}Track Your Order | YummyTummy Shop{% endblock %}

{% block extra_css %}
<style>
    .tracking-section {
        padding: 60px 0;
        background-color: var(--accent-color);
    }

    .tracking-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .tracking-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .tracking-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
        color: var(--primary-color);
    }

    .tracking-subtitle {
        font-size: 1.1rem;
        color: var(--dark-gray);
        margin-bottom: 20px;
    }

    .tracking-form {
        background-color: var(--secondary-color);
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        margin-bottom: 30px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--primary-color);
    }

    .form-input {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid var(--light-gray);
        border-radius: 5px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
    }

    .track-btn {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 12px 30px;
        border: none;
        border-radius: 5px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }

    .track-btn:hover {
        background-color: #4a2a00;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(89, 53, 0, 0.3);
    }

    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        border-left: 4px solid #dc3545;
    }

    .order-details {
        background-color: var(--secondary-color);
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        margin-bottom: 30px;
    }

    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .order-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .order-status {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
    }

    .status-processing {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-shipped {
        background-color: #cce5ff;
        color: #004085;
    }

    .progress-container {
        margin: 30px 0;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background-color: var(--light-gray);
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .progress-fill {
        height: 100%;
        background-color: var(--primary-color);
        transition: width 0.3s ease;
    }

    .progress-text {
        text-align: center;
        font-weight: 600;
        color: var(--primary-color);
    }

    .tracking-history {
        margin-top: 30px;
    }

    .tracking-step {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid var(--light-gray);
    }

    .tracking-step:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .step-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: var(--secondary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .step-content {
        flex: 1;
    }

    .step-title {
        font-weight: 600;
        margin-bottom: 5px;
        color: var(--primary-color);
    }

    .step-message {
        color: var(--dark-gray);
        margin-bottom: 5px;
    }

    .step-date {
        font-size: 0.9rem;
        color: #999;
    }

    .login-prompt {
        background-color: var(--highlight-color);
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        margin-top: 30px;
    }

    .login-prompt h3 {
        margin-bottom: 10px;
        color: var(--primary-color);
    }

    .login-btn {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 10px 20px;
        border-radius: 5px;
        text-decoration: none;
        font-weight: 600;
        display: inline-block;
        margin-top: 10px;
        transition: all 0.3s ease;
    }

    .login-btn:hover {
        background-color: #4a2a00;
        color: var(--secondary-color);
        text-decoration: none;
        transform: translateY(-2px);
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .tracking-section {
            padding: 40px 0;
        }

        .tracking-title {
            font-size: 1.5rem;
        }

        .tracking-form,
        .order-details {
            padding: 20px;
        }

        .order-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .order-number {
            font-size: 1.2rem;
        }

        .tracking-step {
            flex-direction: column;
            text-align: center;
        }

        .step-icon {
            margin: 0 auto 10px auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="tracking-section">
    <div class="container">
        <div class="tracking-container">
            <div class="tracking-header">
                <h1 class="tracking-title">Track Your Order</h1>
                <p class="tracking-subtitle">Enter your order details below to track your YummyTummy order</p>
            </div>

            {% if not order %}
            <!-- Order Tracking Form -->
            <div class="tracking-form">
                {% if error_message %}
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i> {{ error_message }}
                </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="order_number" class="form-label">Order Number</label>
                        <input type="text" id="order_number" name="order_number" class="form-input" 
                               placeholder="e.g., MSL-000123" required>
                        <small style="color: #666; font-size: 0.9rem;">
                            Your order number can be found in your confirmation email
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" id="email" name="email" class="form-input" 
                               placeholder="Enter the email used for your order" required>
                    </div>

                    <button type="submit" class="track-btn">
                        <i class="fas fa-search"></i> Track Order
                    </button>
                </form>
            </div>

            <!-- Login Prompt -->
            <div class="login-prompt">
                <h3>Have an account?</h3>
                <p>Log in to view all your orders and manage your account</p>
                <a href="{% url 'yummytummy_store:order_tracking_dashboard' %}" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i> Login to Your Account
                </a>
            </div>

            {% else %}
            <!-- Order Details and Tracking -->
            <div class="order-details">
                <div class="order-header">
                    <div class="order-number">Order #{{ order.get_order_number }}</div>
                    <div class="order-status status-{{ order.payment_status }}">
                        {{ order.get_payment_status_display }}
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {{ progress_percentage }}%"></div>
                    </div>
                    <div class="progress-text">{{ progress_percentage }}% Complete</div>
                </div>

                <!-- Order Information -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div>
                        <strong>Customer:</strong> {{ order.get_customer_name }}<br>
                        <strong>Order Date:</strong> {% timezone "Africa/Nairobi" %}{{ order.created|date:"F d, Y \a\t H:i" }} EAT{% endtimezone %}<br>
                        <strong>Total:</strong> {{ order.get_formatted_total }}
                    </div>
                    <div>
                        <strong>Payment Method:</strong> {{ order.get_payment_method_display }}<br>
                        {% if order.mpesa_receipt_number %}
                        <strong>M-Pesa Receipt:</strong> {{ order.mpesa_receipt_number }}<br>
                        {% endif %}
                        <strong>Items:</strong> {{ order.items.count }} item{{ order.items.count|pluralize }}
                    </div>
                </div>

                <!-- Tracking History -->
                <div class="tracking-history">
                    <h3 style="margin-bottom: 20px; color: var(--primary-color);">
                        <i class="fas fa-history"></i> Order Progress
                    </h3>
                    
                    {% for status in tracking_history %}
                    <div class="tracking-step">
                        <div class="step-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-title">{{ status.get_status_display }}</div>
                            <div class="step-message">{{ status.message }}</div>
                            <div class="step-date">
                                {% timezone "Africa/Nairobi" %}{{ status.created_at|date:"F d, Y \a\t H:i" }} EAT{% endtimezone %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Login Prompt for Tracked Order -->
            <div class="login-prompt">
                <h3>Want to see all your orders?</h3>
                <p>Create an account or log in to view your complete order history and manage your preferences</p>
                <a href="{% url 'yummytummy_store:order_tracking_dashboard' %}" class="login-btn">
                    <i class="fas fa-user"></i> Access Your Account
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</section>
{% endblock %}
