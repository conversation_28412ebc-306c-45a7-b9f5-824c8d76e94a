{% extends 'yummytummy_store/base.html' %}
{% load static %}
{% load tz %}

{% block title %}My Account - Order Tracking{% endblock %}

{% block extra_css %}
<style>
    .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-color), #7a4f00);
        color: var(--secondary-color);
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .dashboard-header h1 {
        margin: 0 0 10px 0;
        font-size: 2.5rem;
    }
    
    .dashboard-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }
    
    .dashboard-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }
    
    .stat-card {
        background: var(--secondary-color);
        border: 2px solid var(--light-gray);
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        border-color: var(--yellow);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: var(--dark-gray);
        font-weight: 600;
    }
    
    .orders-section {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    
    .section-title {
        font-size: 1.8rem;
        color: var(--primary-color);
        margin-bottom: 25px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .order-card {
        border: 2px solid var(--light-gray);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    
    .order-card:hover {
        border-color: var(--yellow);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .order-number {
        font-size: 1.3rem;
        font-weight: bold;
        color: var(--primary-color);
    }
    
    .order-date {
        color: var(--dark-gray);
        font-size: 0.9rem;
    }
    
    .order-status {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .status-processing {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-shipped {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .status-delivered {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .progress-bar {
        background-color: var(--light-gray);
        border-radius: 10px;
        height: 8px;
        margin: 15px 0;
        overflow: hidden;
    }
    
    .progress-fill {
        background: linear-gradient(90deg, var(--yellow), var(--primary-color));
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
    }
    
    .order-details {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 20px;
        align-items: center;
    }
    
    .order-info {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    
    .order-total {
        font-size: 1.2rem;
        font-weight: bold;
        color: var(--primary-color);
    }
    
    .order-items {
        color: var(--dark-gray);
        font-size: 0.9rem;
    }
    
    .view-order-btn {
        background-color: var(--yellow);
        color: var(--primary-color);
        padding: 10px 20px;
        border: none;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .view-order-btn:hover {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        transform: translateY(-2px);
    }
    
    .no-orders {
        text-align: center;
        padding: 40px;
        color: var(--dark-gray);
    }
    
    .no-orders i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: var(--light-gray);
    }
    
    .shop-now-btn {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 12px 30px;
        border: none;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        margin-top: 20px;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .shop-now-btn:hover {
        background-color: var(--yellow);
        color: var(--primary-color);
        transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 15px;
        }
        
        .dashboard-header {
            padding: 20px;
        }
        
        .dashboard-header h1 {
            font-size: 2rem;
        }
        
        .order-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .order-details {
            grid-template-columns: 1fr;
            gap: 15px;
        }
        
        .view-order-btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1>Welcome back, {{ user.first_name }}!</h1>
        <p>Track your orders and manage your YummyTummy account</p>
    </div>

    <!-- Dashboard Stats -->
    <div class="dashboard-stats">
        <div class="stat-card">
            <div class="stat-number">{{ orders_with_tracking|length }}</div>
            <div class="stat-label">Total Orders</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {% for order_data in orders_with_tracking %}
                    {% if order_data.latest_status.status == 'processing' %}{{ forloop.counter0|add:1 }}{% endif %}
                {% empty %}0{% endfor %}
            </div>
            <div class="stat-label">Processing</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {% for order_data in orders_with_tracking %}
                    {% if order_data.latest_status.status == 'delivered' %}{{ forloop.counter0|add:1 }}{% endif %}
                {% empty %}0{% endfor %}
            </div>
            <div class="stat-label">Delivered</div>
        </div>
    </div>

    <!-- Orders Section -->
    <div class="orders-section">
        <h2 class="section-title">
            <i class="fas fa-box"></i>
            Your Orders
        </h2>

        {% if orders_with_tracking %}
            {% for order_data in orders_with_tracking %}
            <div class="order-card">
                <div class="order-header">
                    <div>
                        <div class="order-number">Order #{{ order_data.order.get_order_number }}</div>
                        <div class="order-date">{% timezone "Africa/Nairobi" %}{{ order_data.order.created|date:"F d, Y \a\t H:i" }} EAT{% endtimezone %}</div>
                    </div>
                    <div class="order-status status-{{ order_data.latest_status.status|default:'processing' }}">
                        {% if order_data.latest_status %}
                            {{ order_data.latest_status.get_status_display }}
                        {% else %}
                            Processing
                        {% endif %}
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ order_data.progress_percentage }}%"></div>
                </div>

                <div class="order-details">
                    <div class="order-info">
                        <div class="order-total">{{ order_data.order.get_formatted_total }}</div>
                        <div class="order-items">
                            {{ order_data.order.items.count }} item{{ order_data.order.items.count|pluralize }}
                        </div>
                        {% if order_data.latest_status %}
                        <div class="order-items">
                            Last update: {% timezone "Africa/Nairobi" %}{{ order_data.latest_status.created_at|date:"M d, Y \a\t H:i" }} EAT{% endtimezone %}
                        </div>
                        {% endif %}
                    </div>
                    <a href="{% url 'yummytummy_store:order_detail_tracking' order_data.order.id %}" class="view-order-btn">
                        <i class="fas fa-eye"></i>
                        View Details
                    </a>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-orders">
                <i class="fas fa-shopping-bag"></i>
                <h3>No orders yet</h3>
                <p>Start shopping to see your orders here!</p>
                <a href="{% url 'yummytummy_store:product_list' %}" class="shop-now-btn">
                    <i class="fas fa-shopping-cart"></i>
                    Shop Now
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
