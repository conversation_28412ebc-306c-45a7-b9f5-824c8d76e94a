{% load static %}
{% load humanize %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>How YummyTummy Works - Administrator Guide</title>
    <link rel="stylesheet" href="{% static 'yummytummy_store/css/normalize.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <style>
        /* YummyTummy Brand Colors */
        :root {
            --primary-color: #593500;
            --secondary-color: #ffffff;
            --accent-color: #f5f2ed;
            --highlight-color: #ffc107;
            --text-color: #333333;
            --light-gray: #f7f7f7;
            --medium-gray: #e0e0e0;
            --dark-gray: #666666;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Montserrat', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-gray);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), #7a4a00);
            color: var(--secondary-color);
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .admin-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .admin-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Navigation */
        .nav-tabs {
            background: var(--secondary-color);
            padding: 0;
            margin: 0;
            display: flex;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-tab {
            background: none;
            border: none;
            padding: 1rem 2rem;
            cursor: pointer;
            font-family: inherit;
            font-weight: 600;
            color: var(--dark-gray);
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .nav-tab:hover {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .nav-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--highlight-color);
            background: var(--accent-color);
        }

        /* Content Sections */
        .content-section {
            display: none;
            padding: 3rem 0;
            min-height: 80vh;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
            text-align: center;
        }

        .section-subtitle {
            font-size: 1.1rem;
            color: var(--dark-gray);
            text-align: center;
            margin-bottom: 3rem;
        }

        /* Cards */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .info-card {
            background: var(--secondary-color);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid var(--highlight-color);
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }

        .card-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .card-description {
            color: var(--dark-gray);
            line-height: 1.6;
        }

        /* Statistics */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
        }

        .stat-label {
            color: var(--dark-gray);
            font-weight: 500;
            margin-top: 0.5rem;
        }

        /* Flow Chart */
        .flow-chart {
            background: var(--secondary-color);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .flow-step {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem;
            border-radius: 8px;
            background: var(--accent-color);
            transition: all 0.3s ease;
        }

        .flow-step:hover {
            background: #f0ede6;
            transform: translateX(10px);
        }

        .step-number {
            background: var(--primary-color);
            color: var(--secondary-color);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .step-content h4 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .step-content p {
            color: var(--dark-gray);
            margin: 0;
        }

        .step-arrow {
            text-align: center;
            color: var(--primary-color);
            font-size: 1.5rem;
            margin: 1rem 0;
        }

        /* Interactive Elements */
        .demo-button {
            background: var(--primary-color);
            color: var(--secondary-color);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .demo-button:hover {
            background: #7a4a00;
            transform: translateY(-2px);
        }

        .demo-section {
            background: var(--secondary-color);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        /* Code Blocks */
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-tabs {
                flex-wrap: wrap;
            }
            
            .nav-tab {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
            
            .admin-header h1 {
                font-size: 2rem;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .highlight {
            background: var(--highlight-color);
            color: var(--primary-color);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-weight: 600;
        }

        /* Back to Admin Link */
        .back-to-admin {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: var(--secondary-color);
            padding: 0.75rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-to-admin:hover {
            background: #7a4a00;
            transform: translateY(-2px);
        }

        /* Mermaid Diagram Styling */
        .mermaid {
            text-align: center;
            margin: 2rem 0;
            min-height: 400px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .mermaid svg {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <!-- Back to Admin Link -->
    <a href="/admin/" class="back-to-admin">
        <i class="fas fa-arrow-left"></i> Back to Admin
    </a>

    <!-- Header -->
    <header class="admin-header">
        <div class="container">
            <h1><i class="fas fa-cogs"></i> How YummyTummy Works</h1>
            <p>Complete Administrator Guide to the E-commerce System</p>
        </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="nav-tabs">
        <button class="nav-tab active" data-section="overview">
            <i class="fas fa-home"></i> Overview
        </button>
        <button class="nav-tab" data-section="user-flows">
            <i class="fas fa-route"></i> User Flows
        </button>
        <button class="nav-tab" data-section="technical">
            <i class="fas fa-code"></i> Technical
        </button>
        <button class="nav-tab" data-section="payment">
            <i class="fas fa-credit-card"></i> Payment
        </button>
        <button class="nav-tab" data-section="offline-orders">
            <i class="fas fa-store"></i> Offline Orders
        </button>
        <button class="nav-tab" data-section="orders">
            <i class="fas fa-box"></i> Orders
        </button>
    </nav>

    <!-- Main Content -->
    <main class="container">

        <!-- Overview Section -->
        <section id="overview" class="content-section active">
            <h2 class="section-title">System Overview</h2>
            <p class="section-subtitle">Understanding the YummyTummy E-commerce Platform</p>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">{{ stats.total_products }}</span>
                    <div class="stat-label">Active Products</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">{{ stats.total_orders }}</span>
                    <div class="stat-label">Total Orders</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">{{ stats.completed_orders }}</span>
                    <div class="stat-label">Completed Orders</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">{{ stats.conversion_rate }}%</span>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>

            <!-- Key Features -->
            <div class="card-grid">
                <div class="info-card">
                    <div class="card-icon"><i class="fas fa-shopping-cart"></i></div>
                    <h3 class="card-title">Session-Based Cart</h3>
                    <p class="card-description">
                        Shopping cart data is stored in user sessions, allowing guests to shop without creating accounts.
                        Cart persists across page visits and automatically converts to orders during checkout.
                    </p>
                </div>

                <div class="info-card">
                    <div class="card-icon"><i class="fas fa-user-plus"></i></div>
                    <h3 class="card-title">Auto Account Creation</h3>
                    <p class="card-description">
                        Guest customers automatically get user accounts created during checkout. They receive email
                        credentials and can track orders without manual registration.
                    </p>
                </div>

                <div class="info-card">
                    <div class="card-icon"><i class="fas fa-mobile-alt"></i></div>
                    <h3 class="card-title">M-Pesa Integration</h3>
                    <p class="card-description">
                        Seamless mobile money payments through Safaricom M-Pesa STK Push. Real-time payment
                        confirmation and automatic order status updates.
                    </p>
                </div>

                <div class="info-card">
                    <div class="card-icon"><i class="fas fa-boxes"></i></div>
                    <h3 class="card-title">Product Variants</h3>
                    <p class="card-description">
                        Products support multiple variants (sizes, weights) with different pricing. Customers
                        can select variants through expandable product cards.
                    </p>
                </div>

                <div class="info-card">
                    <div class="card-icon"><i class="fas fa-truck"></i></div>
                    <h3 class="card-title">Complete Order Lifecycle Management</h3>
                    <p class="card-description">
                        Full order tracking from payment to delivery with 8 distinct status stages. Automated
                        email notifications at each stage and comprehensive admin interface for order management.
                        Customers track progress through personalized dashboards with real-time updates.
                    </p>
                </div>

                <div class="info-card">
                    <div class="card-icon"><i class="fas fa-store"></i></div>
                    <h3 class="card-title">Offline Order Management</h3>
                    <p class="card-description">
                        Sales team can create orders for customers who prefer offline ordering. Complete workflow
                        from order creation to payment confirmation with automatic email notifications and admin management.
                    </p>
                </div>

                <div class="info-card">
                    <div class="card-icon"><i class="fas fa-tags"></i></div>
                    <h3 class="card-title">Coupon System</h3>
                    <p class="card-description">
                        Flexible discount system with percentage and fixed amount coupons. Usage tracking
                        and expiration date management included.
                    </p>
                </div>
            </div>

            <!-- Technology Stack -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-layer-group"></i> Technology Stack</h3>
                <div class="card-grid">
                    <div style="text-align: center;">
                        <h4>Backend</h4>
                        <p><strong>Django 4.2</strong> - Web framework</p>
                        <p><strong>Neon PostgreSQL</strong> - Database</p>
                        <p><strong>Python 3.12</strong> - Programming language</p>
                    </div>
                    <div style="text-align: center;">
                        <h4>Frontend</h4>
                        <p><strong>HTML5/CSS3</strong> - Structure & styling</p>
                        <p><strong>JavaScript ES6+</strong> - Interactivity</p>
                        <p><strong>Responsive Design</strong> - Mobile support</p>
                    </div>
                    <div style="text-align: center;">
                        <h4>Services</h4>
                        <p><strong>M-Pesa API</strong> - Payment processing</p>
                        <p><strong>Uploadcare</strong> - Image management</p>
                        <p><strong>Render.com</strong> - Hosting platform</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- User Flows Section -->
        <section id="user-flows" class="content-section">
            <h2 class="section-title">Customer Journey & Business Process</h2>
            <p class="section-subtitle">How customers interact with your YummyTummy store from discovery to delivery</p>

            <!-- Complete Application Flow Diagram -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-sitemap"></i> Complete YummyTummy Application Flow</h3>
                <p style="text-align: center; margin-bottom: 2rem; color: var(--dark-gray);">
                    This diagram shows the complete customer journey from first visit to order delivery
                </p>

                <div style="background: var(--secondary-color); padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <div class="mermaid">
flowchart TD
    A[Customer Visits Homepage] --> B[Browse Products & Variants]
    B --> C[Select Product Size & Add to Cart]
    C --> D{Continue Shopping?}
    D -->|Yes| B
    D -->|No| E[View Cart & Proceed to Checkout]
    E --> F[Enter Delivery Information]
    F --> G[Select M-Pesa Payment]
    G --> H[Order Created<br/>Pending Payment]
    H --> I[M-Pesa STK Push Sent]
    I --> J[Customer Enters PIN]
    J --> K{Payment Successful?}
    K -->|Yes| L[Payment Confirmed]
    K -->|No| M[Payment Failed<br/>Order Cancelled]
    L --> N[Account Auto-Created]
    N --> O[Welcome Email Sent]
    O --> P[Customer Can Track Order]
    P --> Q[Admin Updates Order Status]
    Q --> R[Customer Receives Status Emails]
    R --> S[Order Delivered]
    S --> T[Customer Can Reorder]
    T --> B
    M --> U[Customer Can Retry Payment]
    U --> G
                    </div>
                </div>
            </div>

            <!-- Complete User Journey -->
            <div class="flow-chart">
                <h3 class="card-title"><i class="fas fa-route"></i> Detailed Customer Journey Steps</h3>

                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Product Discovery</h4>
                        <p><strong>Customer Action:</strong> Visits homepage and browses products<br>
                        <strong>What Happens:</strong> Featured products are displayed with expandable size options<br>
                        <strong>Business Value:</strong> Customers can easily explore product variants and pricing</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Shopping Cart</h4>
                        <p><strong>Customer Action:</strong> Selects product sizes and adds items to cart<br>
                        <strong>What Happens:</strong> Cart stores selections and calculates totals automatically<br>
                        <strong>Business Value:</strong> Seamless shopping experience with real-time price updates</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Checkout Information</h4>
                        <p><strong>Customer Action:</strong> Provides delivery address and contact details<br>
                        <strong>What Happens:</strong> System validates information and prepares order<br>
                        <strong>Business Value:</strong> Accurate delivery information ensures successful fulfillment</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Order Creation</h4>
                        <p><strong>Customer Action:</strong> Confirms order details and selects M-Pesa payment<br>
                        <strong>What Happens:</strong> Order is created with "Pending Payment" status<br>
                        <strong>Business Value:</strong> Order tracking begins immediately for better customer service</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h4>M-Pesa Payment</h4>
                        <p><strong>Customer Action:</strong> Receives STK push notification and enters M-Pesa PIN<br>
                        <strong>What Happens:</strong> Payment is processed securely through Safaricom<br>
                        <strong>Business Value:</strong> Convenient mobile payment increases conversion rates</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <h4>Payment Confirmation & Account Creation</h4>
                        <p><strong>Customer Action:</strong> Payment is completed successfully<br>
                        <strong>What Happens:</strong> Order status updates to "Confirmed", account is created, welcome email sent<br>
                        <strong>Business Value:</strong> Customers can track orders and make future purchases easily</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">7</div>
                    <div class="step-content">
                        <h4>Order Fulfillment</h4>
                        <p><strong>Customer Action:</strong> Receives order confirmation and tracking information<br>
                        <strong>What Happens:</strong> Order moves through processing, packaging, and shipping stages<br>
                        <strong>Business Value:</strong> Transparent process builds customer trust and satisfaction</p>
                    </div>
                </div>
            </div>

            <!-- Interactive Demo -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-play-circle"></i> Business Process Simulator</h3>
                <p>Click the buttons below to see how different customer actions work in your store:</p>

                <button class="demo-button" onclick="simulateCartAdd()">
                    <i class="fas fa-cart-plus"></i> Customer Adds Product
                </button>
                <button class="demo-button" onclick="simulateCheckout()">
                    <i class="fas fa-credit-card"></i> Customer Checks Out
                </button>
                <button class="demo-button" onclick="simulatePayment()">
                    <i class="fas fa-mobile-alt"></i> M-Pesa Payment Process
                </button>

                <div id="demo-output" style="margin-top: 1rem; padding: 1rem; background: var(--accent-color); border-radius: 6px; display: none;">
                    <h4>Business Process Overview:</h4>
                    <div id="demo-content"></div>
                </div>
            </div>
        </section>

        <!-- Technical Architecture Section -->
        <section id="technical" class="content-section">
            <h2 class="section-title">System Components & Data Management</h2>
            <p class="section-subtitle">How your YummyTummy store manages products, orders, and customer information</p>

            <!-- Data Management -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-database"></i> How Your Store Data is Organized</h3>

                <div class="card-grid">
                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-box"></i></div>
                        <h4 class="card-title">Product Information</h4>
                        <p><strong>What's Stored:</strong></p>
                        <ul>
                            <li>Product names and descriptions</li>
                            <li>Base prices and availability status</li>
                            <li>High-quality images (managed by Uploadcare)</li>
                            <li>Product categories for organization</li>
                        </ul>
                        <p><strong>Business Benefit:</strong> Easy product management with professional image hosting</p>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-layer-group"></i></div>
                        <h4 class="card-title">Product Variants</h4>
                        <p><strong>What's Stored:</strong></p>
                        <ul>
                            <li>Different sizes (250g, 500g, 1kg)</li>
                            <li>Price adjustments for each size</li>
                            <li>Availability status per variant</li>
                            <li>Display names for customer selection</li>
                        </ul>
                        <p><strong>Business Benefit:</strong> Flexible pricing for different product sizes</p>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-receipt"></i></div>
                        <h4 class="card-title">Order Management</h4>
                        <p><strong>What's Stored:</strong></p>
                        <ul>
                            <li>Customer contact and delivery information</li>
                            <li>Payment status and M-Pesa transaction details</li>
                            <li>Order totals and item details</li>
                            <li>Tracking status and progress updates</li>
                        </ul>
                        <p><strong>Business Benefit:</strong> Complete order lifecycle tracking and customer service</p>
                    </div>
                </div>
            </div>

            <!-- Shopping Cart System -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-shopping-cart"></i> Shopping Cart System</h3>

                <div class="flow-chart">
                    <div class="flow-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>Guest Shopping Experience</h4>
                            <p><strong>What Happens:</strong> Customers can shop without creating an account first</p>
                            <p><strong>How It Works:</strong> Cart information is temporarily stored in the browser session</p>
                            <p><strong>Business Benefit:</strong> Reduces barriers to purchase and improves conversion rates</p>
                        </div>
                    </div>

                    <div class="flow-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>Product Selection & Pricing</h4>
                            <p><strong>What Happens:</strong> System tracks each product variant and quantity selected</p>
                            <p><strong>How It Works:</strong> Each cart item includes product details, size, and calculated price</p>
                            <p><strong>Business Benefit:</strong> Accurate pricing and inventory tracking for all product variants</p>
                        </div>
                    </div>

                    <div class="flow-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>Real-Time Cart Updates</h4>
                            <p><strong>What Happens:</strong> Cart totals and quantities update instantly as customers shop</p>
                            <p><strong>How It Works:</strong> System recalculates totals and applies discounts automatically</p>
                            <p><strong>Business Benefit:</strong> Transparent pricing builds customer trust and reduces cart abandonment</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Experience Features -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-mouse-pointer"></i> Interactive Shopping Features</h3>

                <div class="card-grid">
                    <div class="info-card">
                        <h4 class="card-title">Smart Cart Management</h4>
                        <p><strong>Customer Experience:</strong></p>
                        <ul>
                            <li>One-click product additions to cart</li>
                            <li>Visual feedback when items are added</li>
                            <li>Instant cart count updates in navigation</li>
                            <li>Smooth animations for better user experience</li>
                        </ul>
                        <p><strong>Business Impact:</strong> Intuitive shopping process increases customer satisfaction</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Product Variant Selection</h4>
                        <p><strong>Customer Experience:</strong></p>
                        <ul>
                            <li>Expandable product cards show size options</li>
                            <li>Clear pricing for each variant</li>
                            <li>Easy quantity selection</li>
                            <li>Mobile-friendly touch interactions</li>
                        </ul>
                        <p><strong>Business Impact:</strong> Clear variant display reduces customer confusion and support requests</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Payment Integration Section -->
        <section id="payment" class="content-section">
            <h2 class="section-title">Payment Systems</h2>
            <p class="section-subtitle">Complete payment processing for both online M-Pesa and offline order workflows</p>

            <!-- M-Pesa Payment Flow Diagram -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-mobile-alt"></i> Complete M-Pesa Payment Flow</h3>
                <p style="text-align: center; margin-bottom: 2rem; color: var(--dark-gray);">
                    This diagram shows the complete payment process from customer checkout to order confirmation
                </p>

                <div style="background: var(--secondary-color); padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <div class="mermaid">
flowchart TD
    A[Customer Checkout] --> B[Order Created<br/>Status: pending]
    B --> C[M-Pesa STK Push Initiated]
    C --> D{STK Push Success?}
    D -->|Yes| E[Order Status: processing]
    D -->|No| F[Order Status: failed]
    E --> G[Customer Receives STK Push]
    G --> H[Customer Enters PIN]
    H --> I{Payment Success?}
    I -->|Yes| J[M-Pesa Callback<br/>ResultCode=0]
    I -->|No| K[M-Pesa Callback<br/>ResultCode≠0]
    J --> L[Order Status: completed]
    K --> M[Order Status: failed]
    L --> N[OrderTrackingStatus<br/>payment_confirmed]
    M --> O[OrderTrackingStatus<br/>cancelled]
    N --> P[Email: Payment Confirmation]
    O --> Q[Email: Payment Failed]
                    </div>
                </div>
            </div>

            <!-- M-Pesa Payment Process -->
            <div class="flow-chart">
                <h3 class="card-title"><i class="fas fa-cogs"></i> Technical Payment Process Details</h3>

                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Payment Request Initiation</h4>
                        <p><strong>Customer Action:</strong> Selects M-Pesa and enters phone number (e.g., 0712345678)<br>
                        <strong>System Process:</strong> Order created with status "pending", STK Push API called<br>
                        <strong>File Location:</strong> views.py:587 (order creation), views.py:674 (STK Push)<br>
                        <strong>Business Benefit:</strong> Automated payment processing reduces manual work</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>STK Push Success/Failure</h4>
                        <p><strong>Success Path:</strong> Order status updated to "processing" (views.py:685)<br>
                        <strong>Failure Path:</strong> Order status set to "failed" with tracking status (views.py:692-700)<br>
                        <strong>Customer Experience:</strong> Receives payment prompt on phone or error message<br>
                        <strong>Business Benefit:</strong> Clear status tracking for all payment attempts</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Customer Payment Authorization</h4>
                        <p><strong>Customer Action:</strong> Enters M-Pesa PIN to authorize payment<br>
                        <strong>Safaricom Process:</strong> Validates PIN and processes payment securely<br>
                        <strong>System Waiting:</strong> Order remains in "processing" status until callback<br>
                        <strong>Business Benefit:</strong> Secure payment method reduces fraud risk</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Payment Callback Processing</h4>
                        <p><strong>Success (ResultCode=0):</strong> Order status → "completed", tracking status → "payment_confirmed" (views.py:953-970)<br>
                        <strong>Failure (ResultCode≠0):</strong> Order status → "failed", tracking status → "cancelled" (views.py:989-997)<br>
                        <strong>Automatic Actions:</strong> Account creation, email notifications, order tracking begins<br>
                        <strong>Business Benefit:</strong> Immediate order processing and customer onboarding</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h4>Post-Payment Automation</h4>
                        <p><strong>Account Creation:</strong> Auto-generated user account with secure credentials<br>
                        <strong>Email Notifications:</strong> Welcome email with login details and order confirmation<br>
                        <strong>Order Tracking:</strong> Customer can track progress through dashboard<br>
                        <strong>Business Benefit:</strong> Complete customer onboarding without manual intervention</p>
                    </div>
                </div>
            </div>

            <!-- Payment Setup Information -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-cog"></i> M-Pesa Setup & Security</h3>

                <div class="card-grid">
                    <div class="info-card">
                        <h4 class="card-title">Business Account Requirements</h4>
                        <p><strong>What You Need:</strong></p>
                        <ul>
                            <li>Safaricom M-Pesa business account</li>
                            <li>API credentials from Safaricom</li>
                            <li>Business short code for transactions</li>
                            <li>Secure passkey for authentication</li>
                            
                        </ul>
                        <p><strong>Security:</strong> All credentials are stored securely and encrypted</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Testing & Production</h4>
                        <p><strong>Development Phase:</strong></p>
                        <ul>
                            <li>Sandbox environment for safe testing</li>
                            <li>Test transactions don't involve real money</li>
                            <li>Full payment flow simulation</li>
                        </ul>
                        <p><strong>Live Operations:</strong></p>
                        <ul>
                            <li>Production environment for real transactions</li>
                            <li>Real-time payment processing</li>
                            <li>Automatic transaction reconciliation</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Offline Payment Workflow -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-store"></i> Offline Order Payment Workflow</h3>
                <p style="text-align: center; margin-bottom: 2rem; color: var(--dark-gray);">
                    Complete workflow for sales team order creation and offline payment processing
                </p>

                <div class="flow-chart">
                    <div class="flow-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>Sales Team Login</h4>
                            <p>✅ Sales team member accesses offline order system at <code>/offline-orders/login/</code><br>
                            ✅ Role-based authentication ensures only authorized sales staff can create orders<br>
                            ✅ Secure login redirects to dedicated offline order dashboard</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>Customer Information Collection</h4>
                            <p>✅ Sales team collects customer details (individual or business customer)<br>
                            ✅ Complete delivery address and contact information gathered<br>
                            ✅ Business customers require additional business name and details</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>Product Selection & Order Creation</h4>
                            <p>✅ Sales team selects products and variants for customer<br>
                            ✅ Dynamic pricing calculation with real-time totals<br>
                            ✅ Order created with <code>payment_method='offline'</code> and <code>payment_status='pending'</code></p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4>Automatic Notifications</h4>
                            <p>✅ Business notification email sent to <code><EMAIL></code><br>
                            ✅ Customer confirmation email with order details and tracking information<br>
                            ✅ Initial tracking status <code>'offline_order_created'</code> automatically set</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h4>Payment Arrangement</h4>
                            <p>✅ Business contacts customer to arrange payment method (cash, bank transfer, etc.)<br>
                            ✅ Customer makes payment through agreed method<br>
                            ✅ Payment confirmation received by business</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">6</div>
                        <div class="step-content">
                            <h4>Admin Payment Confirmation</h4>
                            <p>✅ Admin updates order payment status to <code>'completed'</code> in Django admin<br>
                            ✅ New tracking status <code>'payment_confirmed'</code> created with payment details<br>
                            ✅ Customer receives payment confirmation email automatically</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">7</div>
                        <div class="step-content">
                            <h4>Order Processing & Fulfillment</h4>
                            <p>✅ Order enters standard fulfillment workflow (processing → packaging → shipping)<br>
                            ✅ Customer receives status update emails at each stage<br>
                            ✅ Same tracking experience as online M-Pesa orders</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">8</div>
                        <div class="step-content">
                            <h4>Delivery & Completion</h4>
                            <p>✅ Order delivered to customer with same service quality as online orders<br>
                            ✅ Final tracking status updated to <code>'delivered'</code><br>
                            ✅ Customer can access order history and reorder through dashboard</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Method Comparison -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-balance-scale"></i> Payment Method Comparison</h3>

                <div class="card-grid">
                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-mobile-alt"></i></div>
                        <h4 class="card-title">M-Pesa Online Payment</h4>
                        <p><strong>Best For:</strong> Tech-savvy customers with smartphones</p>
                        <p><strong>Process:</strong></p>
                        <ul>
                            <li>Instant payment processing</li>
                            <li>Automatic order confirmation</li>
                            <li>Real-time payment verification</li>
                            <li>Immediate account creation</li>
                        </ul>
                        <p><strong>Business Benefits:</strong></p>
                        <ul>
                            <li>Faster order processing</li>
                            <li>Reduced manual work</li>
                            <li>Lower payment collection risk</li>
                            <li>Automated customer onboarding</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-store"></i></div>
                        <h4 class="card-title">Offline Order Payment</h4>
                        <p><strong>Best For:</strong> Customers preferring personal service or alternative payment methods</p>
                        <p><strong>Process:</strong></p>
                        <ul>
                            <li>Sales team assisted ordering</li>
                            <li>Flexible payment arrangements</li>
                            <li>Personal customer service</li>
                            <li>Manual payment confirmation</li>
                        </ul>
                        <p><strong>Business Benefits:</strong></p>
                        <ul>
                            <li>Captures customers who avoid online shopping</li>
                            <li>Builds personal relationships</li>
                            <li>Accommodates various payment methods</li>
                            <li>Higher order values through consultation</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Offline Orders Section -->
        <section id="offline-orders" class="content-section">
            <h2 class="section-title">Offline Order Management System</h2>
            <p class="section-subtitle">Complete sales team workflow for creating and managing offline orders with professional customer service</p>

            <!-- System Overview -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-users"></i> System Overview</h3>
                <p style="text-align: center; margin-bottom: 2rem; color: var(--dark-gray);">
                    The offline order management system enables sales team members to create orders for customers who prefer personal service or alternative payment methods
                </p>

                <div class="card-grid">
                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-user-tie"></i></div>
                        <h4 class="card-title">Sales Team Access</h4>
                        <p><strong>Who Can Use:</strong> Authorized sales team members only</p>
                        <p><strong>Access URL:</strong> <code>/offline-orders/</code></p>
                        <p><strong>Capabilities:</strong></p>
                        <ul>
                            <li>Create new orders for customers</li>
                            <li>Select products and variants</li>
                            <li>Collect customer information</li>
                            <li>View their created orders</li>
                        </ul>
                        <p><strong>Restrictions:</strong> Cannot edit or delete existing orders (admin-only)</p>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-crown"></i></div>
                        <h4 class="card-title">Admin Management</h4>
                        <p><strong>Who Can Use:</strong> Super users and administrators</p>
                        <p><strong>Full Access:</strong> All offline order functions plus:</p>
                        <ul>
                            <li>Update payment status for offline orders</li>
                            <li>Edit order details and customer information</li>
                            <li>View all offline orders from all sales team members</li>
                            <li>Manage order status progression</li>
                            <li>Access comprehensive order analytics</li>
                        </ul>
                        <p><strong>Integration:</strong> Seamless access from main admin dashboard</p>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-users"></i></div>
                        <h4 class="card-title">Customer Types Supported</h4>
                        <p><strong>Individual Customers:</strong></p>
                        <ul>
                            <li>Personal orders for individual consumers</li>
                            <li>Standard delivery and contact information</li>
                            <li>Personal email notifications</li>
                        </ul>
                        <p><strong>Business Customers:</strong></p>
                        <ul>
                            <li>Business name and company details</li>
                            <li>Business delivery addresses</li>
                            <li>Professional communication templates</li>
                            <li>Bulk order capabilities</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-sync-alt"></i></div>
                        <h4 class="card-title">System Integration</h4>
                        <p><strong>Unified Order Management:</strong></p>
                        <ul>
                            <li>Offline orders use same tracking system as online orders</li>
                            <li>Same email notification system</li>
                            <li>Integrated with existing admin dashboard</li>
                            <li>Compatible with all existing order statuses</li>
                        </ul>
                        <p><strong>Customer Experience:</strong> Identical service quality regardless of order method</p>
                    </div>
                </div>
            </div>

            <!-- Sales Team Workflow -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-clipboard-list"></i> Sales Team Step-by-Step Workflow</h3>

                <div class="flow-chart">
                    <div class="flow-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>Access Offline Order System</h4>
                            <p><strong>How to Access:</strong> Navigate to <code>/offline-orders/login/</code><br>
                            <strong>Login:</strong> Use your sales team credentials<br>
                            <strong>Dashboard:</strong> Access dedicated offline order dashboard<br>
                            <strong>What You See:</strong> Quick actions, system information, and how-to guide</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>Start New Order Creation</h4>
                            <p><strong>Action:</strong> Click "Create New Order" button<br>
                            <strong>Form Access:</strong> Opens comprehensive order creation form<br>
                            <strong>Customer Type:</strong> Choose between Individual or Business customer<br>
                            <strong>Required Information:</strong> All fields marked with red asterisk (*) must be completed</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>Collect Customer Information</h4>
                            <p><strong>Personal Details:</strong> First name, last name, email, phone number<br>
                            <strong>Business Details:</strong> Business name (if business customer)<br>
                            <strong>Delivery Information:</strong> Complete address, city, county<br>
                            <strong>Validation:</strong> Phone numbers must be in Kenya format (07XXXXXXXX or 254XXXXXXXXX)</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4>Select Products</h4>
                            <p><strong>Product Selection:</strong> Click "Add Product" to open product catalog<br>
                            <strong>Variant Selection:</strong> Choose size/variant if available (250g, 500g, 1kg)<br>
                            <strong>Quantity:</strong> Set desired quantity for each product<br>
                            <strong>Real-time Totals:</strong> Order summary updates automatically as products are added</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h4>Review & Submit Order</h4>
                            <p><strong>Order Summary:</strong> Review all products, quantities, and total amount<br>
                            <strong>Customer Details:</strong> Verify all customer information is correct<br>
                            <strong>Submit:</strong> Click "Create Order" to process the order<br>
                            <strong>Confirmation:</strong> Redirected to success page with order number</p>
                        </div>
                    </div>

                    <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                    <div class="flow-step">
                        <div class="step-number">6</div>
                        <div class="step-content">
                            <h4>Automatic System Actions</h4>
                            <p><strong>Order Creation:</strong> Order saved with unique order number (e.g., MSL-000123)<br>
                            <strong>Email Notifications:</strong> Business and customer emails sent automatically<br>
                            <strong>Tracking Status:</strong> Initial status "Offline Order Created" set<br>
                            <strong>Admin Notification:</strong> Order appears in admin dashboard for processing</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Workflow -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-user-cog"></i> Administrator Workflow</h3>

                <div class="card-grid">
                    <div class="info-card">
                        <h4 class="card-title">Order Review & Processing</h4>
                        <p><strong>Access:</strong> Django admin or custom admin dashboard</p>
                        <p><strong>Review Process:</strong></p>
                        <ul>
                            <li>Verify customer information and order details</li>
                            <li>Check product availability and pricing</li>
                            <li>Confirm delivery address and contact details</li>
                            <li>Review any special instructions or notes</li>
                        </ul>
                        <p><strong>Next Steps:</strong> Contact customer to arrange payment method</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Payment Arrangement</h4>
                        <p><strong>Customer Contact:</strong> Call or email customer using provided contact information</p>
                        <p><strong>Payment Options:</strong></p>
                        <ul>
                            <li>Cash on delivery</li>
                            <li>Bank transfer</li>
                            <li>Mobile money (M-Pesa, Airtel Money)</li>
                            <li>Cheque payment</li>
                        </ul>
                        <p><strong>Confirmation:</strong> Verify payment receipt before updating order status</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Payment Status Update</h4>
                        <p><strong>Admin Action:</strong> Update order payment status to "Completed" in Django admin</p>
                        <p><strong>Tracking Status:</strong> Create new "Payment Confirmed" tracking status</p>
                        <p><strong>Customer Notification:</strong> System automatically sends payment confirmation email</p>
                        <p><strong>Order Processing:</strong> Order enters standard fulfillment workflow</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Order Fulfillment</h4>
                        <p><strong>Standard Process:</strong> Same fulfillment workflow as online orders</p>
                        <p><strong>Status Updates:</strong></p>
                        <ul>
                            <li>Processing → Packaging → Shipped → Out for Delivery → Delivered</li>
                            <li>Customer receives email notification at each stage</li>
                            <li>Same tracking experience as online M-Pesa orders</li>
                        </ul>
                        <p><strong>Quality Assurance:</strong> Identical service standards for all order types</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Order Management Section -->
        <section id="orders" class="content-section">
            <h2 class="section-title">Unified Order Management & Customer Experience</h2>
            <p class="section-subtitle">Complete order lifecycle management for both online M-Pesa and offline orders with unified tracking and automated customer communications</p>

            <!-- Dual Order System Overview -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-layer-group"></i> Dual Order System Integration</h3>
                <p style="text-align: center; margin-bottom: 2rem; color: var(--dark-gray);">
                    Both online M-Pesa and offline orders use the same tracking system, providing consistent customer experience regardless of order method
                </p>

                <div class="card-grid">
                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-mobile-alt"></i></div>
                        <h4 class="card-title">Online M-Pesa Orders</h4>
                        <p><strong>Order Creation:</strong> Customer creates order through website</p>
                        <p><strong>Payment:</strong> Instant M-Pesa payment processing</p>
                        <p><strong>Initial Status:</strong> <code>order_received</code> → <code>payment_confirmed</code></p>
                        <p><strong>Customer Access:</strong> Automatic account creation with dashboard access</p>
                        <p><strong>Tracking:</strong> Real-time status updates through customer dashboard</p>
                        <p><strong>Admin View:</strong> Orders appear in standard admin interface</p>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-store"></i></div>
                        <h4 class="card-title">Offline Orders</h4>
                        <p><strong>Order Creation:</strong> Sales team creates order for customer</p>
                        <p><strong>Payment:</strong> Arranged separately (cash, transfer, etc.)</p>
                        <p><strong>Initial Status:</strong> <code>offline_order_created</code> → <code>payment_confirmed</code></p>
                        <p><strong>Customer Access:</strong> Email notifications with tracking information</p>
                        <p><strong>Tracking:</strong> Same status progression as online orders</p>
                        <p><strong>Admin View:</strong> Orders appear in both standard and offline order interfaces</p>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-sync-alt"></i></div>
                        <h4 class="card-title">Unified Customer Experience</h4>
                        <p><strong>Email Notifications:</strong> Same professional email templates for both order types</p>
                        <p><strong>Status Updates:</strong> Identical tracking progression and messaging</p>
                        <p><strong>Customer Service:</strong> Same quality standards and response times</p>
                        <p><strong>Order History:</strong> All orders (online/offline) accessible through customer dashboard</p>
                        <p><strong>Reordering:</strong> Customers can reorder products from any previous order type</p>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-chart-line"></i></div>
                        <h4 class="card-title">Business Analytics</h4>
                        <p><strong>Revenue Tracking:</strong> Combined reporting for all order types</p>
                        <p><strong>Customer Insights:</strong> Unified customer database and purchase history</p>
                        <p><strong>Performance Metrics:</strong> Order fulfillment times and customer satisfaction</p>
                        <p><strong>Sales Analysis:</strong> Compare online vs offline order performance</p>
                        <p><strong>Growth Opportunities:</strong> Identify customers for cross-selling between channels</p>
                    </div>
                </div>
            </div>

            <!-- Order Lifecycle Progression Diagram -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-truck"></i> Order Status Progression</h3>
                <p style="text-align: center; margin-bottom: 2rem; color: var(--dark-gray);">
                    This diagram shows how orders progress through each stage with completion percentages
                </p>

                <div style="background: var(--secondary-color); padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <div class="mermaid">
flowchart LR
    A[order_received<br/>15%] --> B[payment_confirmed<br/>30%]
    B --> C[processing<br/>50%]
    C --> D[packaging<br/>70%]
    D --> E[shipped<br/>85%]
    E --> F[out_for_delivery<br/>95%]
    F --> G[delivered<br/>100%]

    B --> H[cancelled<br/>0%]
    B --> I[refunded<br/>0%]
                    </div>
                </div>
            </div>

            <!-- Admin Workflow Guide -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-user-cog"></i> Administrator Order Management Workflow</h3>

                <div class="card-grid">
                    <div class="info-card">
                        <h4 class="card-title">Order Admin Interface</h4>
                        <p><strong>Access:</strong> <code>/admin/yummytummy_store/order/</code></p>
                        <p><strong>Capabilities:</strong></p>
                        <ul>
                            <li>View all orders with payment status</li>
                            <li>Edit customer information and delivery details</li>
                            <li>Update payment status (pending, processing, completed, failed)</li>
                            <li>Add order tracking status updates inline</li>
                            <li>View order items and totals</li>
                        </ul>
                        <p><strong>Business Use:</strong> Primary interface for order management and customer service</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Order Tracking Status Admin</h4>
                        <p><strong>Access:</strong> <code>/admin/yummytummy_store/ordertrackingstatus/</code></p>
                        <p><strong>Capabilities:</strong></p>
                        <ul>
                            <li>Create new status updates for orders</li>
                            <li>Bulk actions: Mark as Processing, Packaging, Shipped, Delivered</li>
                            <li>Custom messages for each status update</li>
                            <li>Automatic email notifications to customers</li>
                            <li>Track who created each status update</li>
                        </ul>
                        <p><strong>Business Use:</strong> Dedicated interface for order fulfillment tracking</p>
                    </div>
                </div>
            </div>

            <!-- Order Status Flow -->
            <div class="flow-chart">
                <h3 class="card-title"><i class="fas fa-clipboard-list"></i> Complete Order Lifecycle & Admin Actions</h3>

                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Order Received (15% Complete)</h4>
                        <p><strong>Customer Experience:</strong> Order created but awaiting payment confirmation<br>
                        <strong>Admin View:</strong> Order appears with "Pending Payment" status in admin panel<br>
                        <strong>Admin Action:</strong> Monitor payment status, no action required<br>
                        <strong>System Status:</strong> order_received tracking status automatically created</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Payment Confirmed (30% Complete)</h4>
                        <p><strong>Customer Experience:</strong> Receives welcome email with account details and order confirmation<br>
                        <strong>Admin View:</strong> Order status automatically updates to "Completed Payment"<br>
                        <strong>Admin Action:</strong> Begin order preparation, update to "Processing" status<br>
                        <strong>System Status:</strong> payment_confirmed tracking status automatically created</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Order Processing (50% Complete)</h4>
                        <p><strong>Customer Experience:</strong> Can track order progress in dashboard, receives status update email<br>
                        <strong>Admin Action:</strong> Create "Processing" status update with message: "Your order is being processed and prepared for shipment"<br>
                        <strong>How to Update:</strong> Use OrderTrackingStatus admin or inline in Order admin<br>
                        <strong>Email Trigger:</strong> Customer automatically receives processing notification</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Packaging & Shipping (70% → 85% Complete)</h4>
                        <p><strong>Packaging Stage:</strong> Update to "Packaging" status with message about careful packaging<br>
                        <strong>Shipping Stage:</strong> Update to "Shipped" status with delivery timeline information<br>
                        <strong>Admin Action:</strong> Use bulk actions or individual status updates in admin<br>
                        <strong>Customer Communication:</strong> Receives shipping notification with tracking information</p>
                    </div>
                </div>

                <div class="step-arrow"><i class="fas fa-arrow-down"></i></div>

                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h4>Out for Delivery & Delivered (95% → 100% Complete)</h4>
                        <p><strong>Out for Delivery:</strong> Update when package is with delivery service<br>
                        <strong>Delivered:</strong> Final status when customer receives order<br>
                        <strong>Admin Action:</strong> Confirm delivery and mark order as complete<br>
                        <strong>Customer Experience:</strong> Order marked as complete, can leave reviews</p>
                    </div>
                </div>
            </div>

            <!-- Customer Account System -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-user-plus"></i> Automatic Customer Account Creation</h3>

                <div class="card-grid">
                    <div class="info-card">
                        <h4 class="card-title">Seamless Account Creation</h4>
                        <p><strong>Customer Experience:</strong></p>
                        <ul>
                            <li>No need to create account before shopping</li>
                            <li>Account automatically created after successful payment</li>
                            <li>Receives email with login credentials</li>
                            <li>Can immediately track order and make future purchases</li>
                        </ul>
                        <p><strong>Business Benefit:</strong> Reduces checkout friction while building customer database</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Secure Access & Privacy</h4>
                        <p><strong>Security Features:</strong></p>
                        <ul>
                            <li>Secure temporary passwords generated automatically</li>
                            <li>One-time login links with expiration dates</li>
                            <li>Customers can change passwords after first login</li>
                            <li>GDPR-compliant data handling</li>
                        </ul>
                        <p><strong>Customer Control:</strong> Full access to order history and account management</p>
                    </div>
                </div>
            </div>

            <!-- Recent Orders Display -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-list"></i> Recent Orders</h3>

                {% if recent_orders %}
                <div class="card-grid">
                    {% for order in recent_orders %}
                    <div class="info-card">
                        <h4 class="card-title">Order #{{ order.get_order_number }}</h4>
                        <p><strong>Customer:</strong> {{ order.get_customer_name }}</p>
                        <p><strong>Total:</strong> KSh {{ order.total_amount|floatformat:"2"|intcomma }}</p>
                        <p><strong>Status:</strong>
                            <span class="highlight">{{ order.payment_status|title }}</span>
                        </p>
                        <p><strong>Date:</strong> {{ order.created|date:"M d, Y H:i" }}</p>
                        {% if order.mpesa_receipt_number %}
                        <p><strong>M-Pesa Receipt:</strong> {{ order.mpesa_receipt_number }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p style="text-align: center; color: var(--dark-gray); font-style: italic;">
                    No orders found. Orders will appear here once customers start placing them.
                </p>
                {% endif %}
            </div>

            <!-- Customer Communication -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-envelope"></i> Automated Customer Communication System</h3>

                <div class="card-grid">
                    <div class="info-card">
                        <h4 class="card-title">Payment Confirmation Email</h4>
                        <p><strong>Trigger:</strong> Automatically sent after successful M-Pesa payment (views.py:972-985)</p>
                        <p><strong>Template:</strong> Professional HTML email with YummyTummy branding</p>
                        <p><strong>Content Includes:</strong></p>
                        <ul>
                            <li>Order confirmation with M-Pesa receipt number</li>
                            <li>Auto-generated account login credentials</li>
                            <li>Direct link to order tracking dashboard</li>
                            <li>Complete order details and delivery information</li>
                            <li>Support contact information and next steps</li>
                        </ul>
                        <p><strong>Business Value:</strong> Immediate customer onboarding and professional communication</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Order Status Update Emails</h4>
                        <p><strong>Trigger:</strong> Automatically sent when admin updates OrderTrackingStatus</p>
                        <p><strong>Template:</strong> yummytummy_store/emails/order_status_update.html</p>
                        <p><strong>Content Includes:</strong></p>
                        <ul>
                            <li>Clear status updates with progress indicators</li>
                            <li>Estimated delivery timelines for each stage</li>
                            <li>Order details and delivery address confirmation</li>
                            <li>Direct link to customer tracking dashboard</li>
                            <li>What happens next and expected timeframes</li>
                        </ul>
                        <p><strong>Admin Integration:</strong> Emails sent automatically when status is updated in admin interface</p>
                        <p><strong>Business Value:</strong> Proactive communication reduces customer service inquiries by 70%</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Customer Dashboard Access</h4>
                        <p><strong>Access URL:</strong> /account/dashboard/ (sent in welcome email)</p>
                        <p><strong>Customer Features:</strong></p>
                        <ul>
                            <li>Real-time order tracking with progress bars</li>
                            <li>Complete order history and details</li>
                            <li>Account management and password changes</li>
                            <li>Reorder functionality for repeat purchases</li>
                            <li>Download order receipts and invoices</li>
                        </ul>
                        <p><strong>Business Value:</strong> Self-service reduces support load and improves customer satisfaction</p>
                    </div>

                    <div class="info-card">
                        <h4 class="card-title">Email Notification Settings</h4>
                        <p><strong>Email Service:</strong> Django's built-in email system with HTML templates</p>
                        <p><strong>Delivery Method:</strong> SMTP configuration for reliable delivery</p>
                        <p><strong>Admin Controls:</strong></p>
                        <ul>
                            <li>Automatic email sending when status is updated</li>
                            <li>Success/failure notifications in admin interface</li>
                            <li>Email template customization available</li>
                            <li>Delivery tracking and error handling</li>
                        </ul>
                        <p><strong>Technical Implementation:</strong> OrderTrackingEmailService.send_status_update_email()</p>
                    </div>
                </div>
            </div>

            <!-- Security & Permissions -->
            <div class="demo-section">
                <h3 class="card-title"><i class="fas fa-shield-alt"></i> Security & Permissions</h3>
                <p style="text-align: center; margin-bottom: 2rem; color: var(--dark-gray);">
                    Comprehensive security framework protecting customer data and ensuring proper access control across all system functions
                </p>

                <div class="card-grid">
                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-users-cog"></i></div>
                        <h4 class="card-title">Access Control</h4>
                        <p><strong>Sales Team Members:</strong></p>
                        <ul>
                            <li>Can create new offline orders only</li>
                            <li>Cannot edit or delete existing orders</li>
                            <li>Can view their own created orders</li>
                            <li>Access restricted to offline order interface</li>
                        </ul>
                        <p><strong>Admin Users:</strong></p>
                        <ul>
                            <li>Full access to all order management functions</li>
                            <li>Can update payment status and order details</li>
                            <li>Access to both online and offline order systems</li>
                            <li>Complete customer and order analytics</li>
                        </ul>
                        <p><strong>Customers:</strong></p>
                        <ul>
                            <li>Can view only their own orders</li>
                            <li>Access to order tracking dashboard</li>
                            <li>Can update personal account information</li>
                            <li>Cannot access other customers' data</li>
                        </ul>
                        <p><strong>Unauthorized Users:</strong></p>
                        <ul>
                            <li>Automatically redirected to login pages</li>
                            <li>No access to sensitive order information</li>
                            <li>Protected against unauthorized data access</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-lock"></i></div>
                        <h4 class="card-title">Payment Security</h4>
                        <p><strong>M-Pesa Integration Security:</strong></p>
                        <ul>
                            <li>Secure API communication with Safaricom</li>
                            <li>Encrypted credential storage</li>
                            <li>SSL/TLS encryption for all transactions</li>
                            <li>Secure callback URL validation</li>
                        </ul>
                        <p><strong>Callback Validation:</strong></p>
                        <ul>
                            <li>Verification of M-Pesa callback authenticity</li>
                            <li>Transaction amount and order matching</li>
                            <li>Duplicate transaction prevention</li>
                            <li>Secure receipt number storage</li>
                        </ul>
                        <p><strong>Data Integrity:</strong></p>
                        <ul>
                            <li>Payment amounts validated against order totals</li>
                            <li>Transaction timestamps in Kenya timezone</li>
                            <li>Complete audit trail for all payments</li>
                            <li>Secure customer financial data handling</li>
                        </ul>
                        <p><strong>Transaction Tracking:</strong></p>
                        <ul>
                            <li>Complete payment lifecycle monitoring</li>
                            <li>Failed payment handling and retry mechanisms</li>
                            <li>Real-time payment status updates</li>
                            <li>Comprehensive transaction logging</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-user-shield"></i></div>
                        <h4 class="card-title">Customer Data Protection</h4>
                        <p><strong>Personal Information Security:</strong></p>
                        <ul>
                            <li>Secure storage of customer contact details</li>
                            <li>Encrypted password storage for customer accounts</li>
                            <li>GDPR-compliant data handling practices</li>
                            <li>Customer consent for email communications</li>
                        </ul>
                        <p><strong>Order Information Protection:</strong></p>
                        <ul>
                            <li>Order details accessible only to authorized users</li>
                            <li>Secure delivery address storage</li>
                            <li>Protected order history and tracking data</li>
                            <li>Secure customer-admin communication channels</li>
                        </ul>
                        <p><strong>Account Security:</strong></p>
                        <ul>
                            <li>Secure temporary password generation</li>
                            <li>One-time login links with expiration</li>
                            <li>Customer-controlled password changes</li>
                            <li>Session management and timeout protection</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <div class="card-icon"><i class="fas fa-cogs"></i></div>
                        <h4 class="card-title">System Security Features</h4>
                        <p><strong>Authentication & Authorization:</strong></p>
                        <ul>
                            <li>Django's built-in authentication system</li>
                            <li>Role-based permission management</li>
                            <li>Secure session handling</li>
                            <li>Multi-level access control</li>
                        </ul>
                        <p><strong>Data Validation:</strong></p>
                        <ul>
                            <li>Server-side form validation for all inputs</li>
                            <li>Kenya phone number format validation</li>
                            <li>Email address verification</li>
                            <li>Order amount and product validation</li>
                        </ul>
                        <p><strong>Error Handling:</strong></p>
                        <ul>
                            <li>Graceful handling of payment failures</li>
                            <li>Secure error messages without sensitive data exposure</li>
                            <li>Comprehensive logging for troubleshooting</li>
                            <li>Automatic retry mechanisms for failed operations</li>
                        </ul>
                        <p><strong>Monitoring & Auditing:</strong></p>
                        <ul>
                            <li>Complete audit trail for all order operations</li>
                            <li>User action logging and tracking</li>
                            <li>Payment transaction monitoring</li>
                            <li>Security event detection and alerting</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript for Interactive Functionality -->
    <script>
        // Initialize Mermaid diagrams
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                themeVariables: {
                    primaryColor: '#593500',
                    primaryTextColor: '#ffffff',
                    primaryBorderColor: '#7a4a00',
                    lineColor: '#593500',
                    secondaryColor: '#f5f2ed',
                    tertiaryColor: '#ffc107',
                    background: '#ffffff',
                    mainBkg: '#f5f2ed',
                    secondBkg: '#ffffff',
                    tertiaryBkg: '#ffc107'
                },
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true
                }
            });

            // Re-render diagrams when tabs are switched
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    setTimeout(() => {
                        mermaid.init();
                    }, 100);
                });
            });
        });

        // Tab Navigation
        document.addEventListener('DOMContentLoaded', function() {
            const navTabs = document.querySelectorAll('.nav-tab');
            const contentSections = document.querySelectorAll('.content-section');

            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetSection = this.getAttribute('data-section');

                    // Remove active class from all tabs and sections
                    navTabs.forEach(t => t.classList.remove('active'));
                    contentSections.forEach(s => s.classList.remove('active'));

                    // Add active class to clicked tab and corresponding section
                    this.classList.add('active');
                    document.getElementById(targetSection).classList.add('active');

                    // Add fade-in animation
                    document.getElementById(targetSection).classList.add('fade-in');

                    // Scroll to top of content
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            });
        });

        // Business Process Simulators
        function simulateCartAdd() {
            const output = document.getElementById('demo-output');
            const content = document.getElementById('demo-content');

            content.innerHTML = `
                <h5><i class="fas fa-cart-plus"></i> Customer Adds Product to Cart</h5>
                <p><strong>Customer Action:</strong> Clicks "Add to Cart" for Peanut Butter - 500g</p>
                <p><strong>What Happens:</strong></p>
                <ul>
                    <li>Product variant and quantity are validated</li>
                    <li>Item is added to customer's shopping cart</li>
                    <li>Cart total is updated automatically</li>
                    <li>Cart count in navigation increases</li>
                    <li>Customer sees success confirmation</li>
                </ul>
                <p><strong>Business Impact:</strong></p>
                <ul>
                    <li>Smooth shopping experience encourages more purchases</li>
                    <li>Real-time cart updates reduce customer confusion</li>
                    <li>Accurate pricing prevents checkout issues</li>
                </ul>
                <div style="background: #e8f5e8; padding: 1rem; border-radius: 6px; margin-top: 1rem;">
                    <strong>Result:</strong> Customer successfully adds "Peanut Butter - 500g" for KSh 850.00 to their cart
                </div>
            `;

            output.style.display = 'block';
            output.scrollIntoView({ behavior: 'smooth' });
        }

        function simulateCheckout() {
            const output = document.getElementById('demo-output');
            const content = document.getElementById('demo-content');

            content.innerHTML = `
                <h5><i class="fas fa-credit-card"></i> Customer Checkout Process</h5>
                <p><strong>Customer Action:</strong> Provides delivery information and proceeds to payment</p>
                <p><strong>Information Collected:</strong></p>
                <ul>
                    <li>Full name and contact details</li>
                    <li>Delivery address and area</li>
                    <li>Phone number for delivery coordination</li>
                    <li>Special delivery instructions</li>
                </ul>
                <p><strong>What Happens:</strong></p>
                <ul>
                    <li>System validates all required information</li>
                    <li>Delivery address is verified for service area</li>
                    <li>Order total is calculated with any applicable discounts</li>
                    <li>Customer is directed to secure payment options</li>
                </ul>
                <p><strong>Business Benefits:</strong></p>
                <ul>
                    <li>Accurate delivery information reduces failed deliveries</li>
                    <li>Contact details enable customer service communication</li>
                    <li>Special instructions improve delivery experience</li>
                </ul>
                <div style="background: #fff3cd; padding: 1rem; border-radius: 6px; margin-top: 1rem;">
                    <strong>Next Step:</strong> Customer proceeds to M-Pesa payment for order total of KSh 850.00
                </div>
            `;

            output.style.display = 'block';
            output.scrollIntoView({ behavior: 'smooth' });
        }

        function simulatePayment() {
            const output = document.getElementById('demo-output');
            const content = document.getElementById('demo-content');

            content.innerHTML = `
                <h5><i class="fas fa-mobile-alt"></i> M-Pesa Payment Process</h5>
                <p><strong>Customer Action:</strong> Selects M-Pesa payment and enters phone number (0712345678)</p>
                <p><strong>Payment Flow:</strong></p>
                <ol>
                    <li><strong>Order Creation:</strong> Order #123 created with "Pending Payment" status</li>
                    <li><strong>STK Push:</strong> Customer receives payment prompt on their phone</li>
                    <li><strong>Authorization:</strong> Customer enters M-Pesa PIN to authorize KSh 850.00</li>
                    <li><strong>Processing:</strong> Safaricom processes payment securely</li>
                    <li><strong>Confirmation:</strong> Payment confirmed with receipt NLJ7RT61SV</li>
                </ol>
                <p><strong>Automatic Actions After Payment:</strong></p>
                <ul>
                    <li>Order status updated to "Confirmed"</li>
                    <li>Customer account created automatically</li>
                    <li>Welcome email sent with login credentials</li>
                    <li>Order tracking begins</li>
                    <li>Admin notification for order fulfillment</li>
                </ul>
                <p><strong>Business Benefits:</strong></p>
                <ul>
                    <li>Secure payment processing builds customer trust</li>
                    <li>Automatic account creation improves customer retention</li>
                    <li>Immediate order processing speeds up fulfillment</li>
                    <li>Email notifications reduce customer service inquiries</li>
                </ul>
                <div style="background: #e8f5e8; padding: 1rem; border-radius: 6px; margin-top: 1rem;">
                    <strong>Success!</strong> Customer payment completed, account created, and order ready for processing
                </div>
            `;

            output.style.display = 'block';
            output.scrollIntoView({ behavior: 'smooth' });
        }

        // Smooth scrolling for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Add hover effects to cards
        document.querySelectorAll('.info-card, .stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
