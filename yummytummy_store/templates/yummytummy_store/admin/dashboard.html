{% load static %}
{% load humanize %}
{% load tz %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YummyTummy Admin Dashboard</title>
    <link rel="stylesheet" href="{% static 'yummytummy_store/css/normalize.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* YummyTummy Brand Colors */
        :root {
            --primary-color: #593500;
            --secondary-color: #ffffff;
            --accent-color: #f5f2ed;
            --highlight-color: #ffc107;
            --text-color: #333333;
            --light-gray: #f7f7f7;
            --medium-gray: #e0e0e0;
            --dark-gray: #666666;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Montserrat', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-gray);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), #7a4a00);
            color: var(--secondary-color);
            padding: 2rem 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-title h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            background: var(--highlight-color);
            color: var(--primary-color);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .quick-action-btn:hover {
            background: #e6ac00;
            transform: translateY(-2px);
        }

        /* Breadcrumbs */
        .breadcrumbs {
            background: var(--secondary-color);
            padding: 1rem 0;
            border-bottom: 1px solid var(--medium-gray);
        }

        .breadcrumb-list {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .breadcrumb-item {
            color: var(--dark-gray);
        }

        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .breadcrumb-item:not(:last-child)::after {
            content: '>';
            margin-left: 0.5rem;
            color: var(--dark-gray);
        }

        /* Alerts */
        .alerts-section {
            padding: 1rem 0;
        }

        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid var(--success-color);
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid var(--warning-color);
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid var(--danger-color);
        }

        /* Main Dashboard */
        .dashboard-main {
            padding: 2rem 0;
        }

        /* Grid Layout */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        /* Cards */
        .dashboard-card {
            background: var(--secondary-color);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }

        .metric-card {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .metric-card:hover {
            transform: translateY(-3px);
        }

        .metric-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .metric-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--dark-gray);
            font-weight: 500;
        }

        .metric-change {
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .metric-change.positive {
            color: var(--success-color);
        }

        .metric-change.negative {
            color: var(--danger-color);
        }

        /* Card Headers */
        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--accent-color);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-action {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--medium-gray);
        }

        .data-table th {
            background: var(--accent-color);
            font-weight: 600;
            color: var(--primary-color);
        }

        .data-table tr:hover {
            background: var(--accent-color);
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #cce7ff;
            color: #004085;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }
            
            .header-title h1 {
                font-size: 2rem;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid var(--accent-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="admin-header">
        <div class="container">
            <div class="header-content">
                <div class="header-title">
                    <i class="fas fa-chart-line"></i>
                    <h1>Admin Dashboard</h1>
                </div>
                <div class="header-actions">
                    <a href="/admin/" class="quick-action-btn">
                        <i class="fas fa-cog"></i> Django Admin
                    </a>
                    <a href="{% url 'yummytummy_store:offline_orders_dashboard' %}" class="quick-action-btn">
                        <i class="fas fa-clipboard-list"></i> Offline Orders
                    </a>
                    <a href="{% url 'yummytummy_store:how_it_works' %}" class="quick-action-btn">
                        <i class="fas fa-info-circle"></i> How It Works
                    </a>
                    <a href="{% url 'yummytummy_store:home' %}" class="quick-action-btn">
                        <i class="fas fa-store"></i> View Store
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Breadcrumbs -->
    <nav class="breadcrumbs">
        <div class="container">
            <ul class="breadcrumb-list">
                <li class="breadcrumb-item"><a href="{% url 'yummytummy_store:home' %}">Home</a></li>
                <li class="breadcrumb-item"><a href="/admin/">Admin</a></li>
                <li class="breadcrumb-item">Dashboard</li>
            </ul>
        </div>
    </nav>

    <!-- Alerts Section -->
    {% if alerts %}
    <section class="alerts-section">
        <div class="container">
            {% for alert in alerts %}
            <div class="alert alert-{{ alert.type }}">
                <i class="fas fa-{% if alert.type == 'success' %}check-circle{% elif alert.type == 'warning' %}exclamation-triangle{% else %}exclamation-circle{% endif %}"></i>
                {{ alert.message }}
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    <!-- Main Dashboard -->
    <main class="dashboard-main">
        <div class="container">

            <!-- Key Metrics Overview -->
            <section class="metrics-section">
                <h2 style="color: var(--primary-color); margin-bottom: 2rem; font-size: 1.8rem;">
                    <i class="fas fa-chart-bar"></i> Business Overview
                </h2>

                <div class="metrics-grid">
                    <!-- Total Revenue -->
                    <div class="metric-card" onclick="location.href='/admin/yummytummy_store/order/'">
                        <div class="metric-icon" style="color: var(--success-color);">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <span class="metric-number">KSh {{ total_revenue|floatformat:"0"|intcomma }}</span>
                        <div class="metric-label">Total Revenue</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i> KSh {{ revenue_this_month|floatformat:"0"|intcomma }} this month
                        </div>
                    </div>

                    <!-- Total Orders -->
                    <div class="metric-card" onclick="location.href='/admin/yummytummy_store/order/'">
                        <div class="metric-icon" style="color: var(--info-color);">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <span class="metric-number">{{ total_orders|intcomma }}</span>
                        <div class="metric-label">Total Orders</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i> {{ orders_this_month }} this month
                        </div>
                    </div>

                    <!-- Completed Orders -->
                    <div class="metric-card">
                        <div class="metric-icon" style="color: var(--success-color);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <span class="metric-number">{{ completed_orders|intcomma }}</span>
                        <div class="metric-label">Completed Orders</div>
                        <div class="metric-change positive">
                            {{ conversion_rate }}% conversion rate
                        </div>
                    </div>

                    <!-- Pending Orders -->
                    <div class="metric-card" onclick="location.href='/admin/yummytummy_store/order/?payment_status__exact=pending'">
                        <div class="metric-icon" style="color: var(--warning-color);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <span class="metric-number">{{ pending_orders|intcomma }}</span>
                        <div class="metric-label">Pending Orders</div>
                        <div class="metric-change">
                            Need attention
                        </div>
                    </div>

                    <!-- Total Customers -->
                    <div class="metric-card" onclick="location.href='/admin/auth/user/'">
                        <div class="metric-icon" style="color: var(--primary-color);">
                            <i class="fas fa-users"></i>
                        </div>
                        <span class="metric-number">{{ total_customers|intcomma }}</span>
                        <div class="metric-label">Total Customers</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i> {{ new_customers_this_month }} new this month
                        </div>
                    </div>

                    <!-- M-Pesa Success Rate -->
                    <div class="metric-card">
                        <div class="metric-icon" style="color: var(--success-color);">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <span class="metric-number">{{ mpesa_success_rate }}%</span>
                        <div class="metric-label">M-Pesa Success Rate</div>
                        <div class="metric-change positive">
                            Reliable payments
                        </div>
                    </div>

                    <!-- Average Order Value -->
                    <div class="metric-card">
                        <div class="metric-icon" style="color: var(--highlight-color);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <span class="metric-number">KSh {{ avg_order_value|floatformat:"0"|intcomma }}</span>
                        <div class="metric-label">Average Order Value</div>
                        <div class="metric-change">
                            Per completed order
                        </div>
                    </div>

                    <!-- Repeat Customer Rate -->
                    <div class="metric-card">
                        <div class="metric-icon" style="color: var(--info-color);">
                            <i class="fas fa-heart"></i>
                        </div>
                        <span class="metric-number">{{ repeat_customer_rate }}%</span>
                        <div class="metric-label">Repeat Customer Rate</div>
                        <div class="metric-change positive">
                            {{ repeat_customers }} loyal customers
                        </div>
                    </div>
                </div>
            </section>

            <!-- Dashboard Cards Grid -->
            <div class="dashboard-grid">

                <!-- Recent Orders -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-receipt"></i> Recent Orders
                        </h3>
                        <a href="/admin/yummytummy_store/order/" class="card-action">View All</a>
                    </div>

                    {% if recent_orders %}
                    <div style="overflow-x: auto;">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr onclick="location.href='/admin/yummytummy_store/order/{{ order.id }}/change/'" style="cursor: pointer;">
                                    <td><strong>#{{ order.get_order_number }}</strong></td>
                                    <td>{{ order.get_customer_name }}</td>
                                    <td>KSh {{ order.total_amount|floatformat:"2"|intcomma }}</td>
                                    <td>
                                        <span class="status-badge status-{{ order.payment_status }}">
                                            {{ order.payment_status|title }}
                                        </span>
                                    </td>
                                    <td>{% timezone "Africa/Nairobi" %}{{ order.created|date:"M d, H:i" }} EAT{% endtimezone %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p style="text-align: center; color: var(--dark-gray); font-style: italic; padding: 2rem;">
                        No orders yet. Orders will appear here once customers start purchasing.
                    </p>
                    {% endif %}
                </div>

                <!-- Best Selling Products -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-star"></i> Best Selling Products
                        </h3>
                        <a href="/admin/yummytummy_store/product/" class="card-action">Manage Products</a>
                    </div>

                    {% if best_selling_products %}
                    <div style="overflow-x: auto;">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Units Sold</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in best_selling_products %}
                                <tr onclick="location.href='/admin/yummytummy_store/product/{{ product.product__id }}/change/'" style="cursor: pointer;">
                                    <td><strong>{{ product.product__name }}</strong></td>
                                    <td>{{ product.total_sold|intcomma }}</td>
                                    <td>KSh {{ product.total_revenue|floatformat:"2"|intcomma }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p style="text-align: center; color: var(--dark-gray); font-style: italic; padding: 2rem;">
                        No sales data yet. Product performance will appear here after orders are completed.
                    </p>
                    {% endif %}
                </div>
            </div>

            <!-- Additional Dashboard Cards -->
            <div class="dashboard-grid">

                <!-- Failed Payments -->
                {% if failed_payments %}
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-exclamation-triangle"></i> Failed Payments (Last 7 Days)
                        </h3>
                        <a href="/admin/yummytummy_store/order/?payment_status__exact=failed" class="card-action">View All</a>
                    </div>

                    <div style="overflow-x: auto;">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in failed_payments %}
                                <tr onclick="location.href='/admin/yummytummy_store/order/{{ order.id }}/change/'" style="cursor: pointer;">
                                    <td><strong>#{{ order.get_order_number }}</strong></td>
                                    <td>{{ order.get_customer_name }}</td>
                                    <td>KSh {{ order.total_amount|floatformat:"2"|intcomma }}</td>
                                    <td>{% timezone "Africa/Nairobi" %}{{ order.created|date:"M d, H:i" }} EAT{% endtimezone %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-clock"></i> Recent Activity
                        </h3>
                        <a href="/admin/yummytummy_store/ordertrackingstatus/" class="card-action">View All</a>
                    </div>

                    {% if recent_tracking_updates %}
                    <div style="max-height: 300px; overflow-y: auto;">
                        {% for update in recent_tracking_updates %}
                        <div style="padding: 1rem; border-bottom: 1px solid var(--medium-gray); cursor: pointer;"
                             onclick="location.href='/admin/yummytummy_store/order/{{ update.order.id }}/change/'">
                            <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 0.5rem;">
                                <strong>Order #{{ update.order.get_order_number }}</strong>
                                <small style="color: var(--dark-gray);">{% timezone "Africa/Nairobi" %}{{ update.created_at|timesince }} ago ({{ update.created_at|date:"H:i" }} EAT){% endtimezone %}</small>
                            </div>
                            <div style="color: var(--dark-gray); font-size: 0.9rem;">
                                <span class="status-badge status-{{ update.status }}">{{ update.status|title }}</span>
                                <br>{{ update.message }}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p style="text-align: center; color: var(--dark-gray); font-style: italic; padding: 2rem;">
                        No recent activity. Order updates will appear here.
                    </p>
                    {% endif %}
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> Quick Actions
                        </h3>
                    </div>

                    <div style="display: grid; gap: 1rem;">
                        <a href="/admin/yummytummy_store/product/add/" class="quick-action-btn" style="justify-self: stretch; text-align: center;">
                            <i class="fas fa-plus"></i> Add New Product
                        </a>
                        <a href="/admin/yummytummy_store/order/" class="quick-action-btn" style="justify-self: stretch; text-align: center;">
                            <i class="fas fa-list"></i> Manage Orders
                        </a>
                        <a href="/admin/auth/user/" class="quick-action-btn" style="justify-self: stretch; text-align: center;">
                            <i class="fas fa-users"></i> Customer Management
                        </a>
                        <a href="/admin/yummytummy_store/coupon/" class="quick-action-btn" style="justify-self: stretch; text-align: center;">
                            <i class="fas fa-tags"></i> Manage Coupons
                        </a>
                        <a href="{% url 'yummytummy_store:how_it_works' %}" class="quick-action-btn" style="justify-self: stretch; text-align: center;">
                            <i class="fas fa-question-circle"></i> How It Works
                        </a>
                    </div>
                </div>

                <!-- System Status -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-server"></i> System Status
                        </h3>
                    </div>

                    <div style="display: grid; gap: 1rem;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>Database</span>
                            <span style="color: var(--success-color);"><i class="fas fa-check-circle"></i> Online</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>M-Pesa Integration</span>
                            <span style="color: var(--success-color);"><i class="fas fa-check-circle"></i> Active</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>Email Service</span>
                            <span style="color: var(--success-color);"><i class="fas fa-check-circle"></i> Working</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>Image CDN</span>
                            <span style="color: var(--success-color);"><i class="fas fa-check-circle"></i> Fast</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>Last Updated</span>
                            <span style="color: var(--dark-gray);">{% timezone "Africa/Nairobi" %}{{ current_time|date:"M d, H:i" }} EAT{% endtimezone %}</span>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <!-- JavaScript for Interactivity -->
    <script>
        // Auto-refresh dashboard every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutes

        // Add click handlers for metric cards
        document.querySelectorAll('.metric-card[onclick]').forEach(card => {
            card.style.cursor = 'pointer';
        });

        // Add loading states for quick actions
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';

                // Restore original icon after navigation
                setTimeout(() => {
                    icon.className = originalClass;
                }, 1000);
            });
        });

        // Add hover effects to table rows
        document.querySelectorAll('.data-table tr[onclick]').forEach(row => {
            row.style.cursor = 'pointer';
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'var(--accent-color)';
            });
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });

        // Display current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('en-KE', {
                timeZone: 'Africa/Nairobi',
                hour12: true,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            // Update any time displays if they exist
            const timeElements = document.querySelectorAll('.current-time');
            timeElements.forEach(el => {
                el.textContent = timeString;
            });
        }

        // Update time every second
        setInterval(updateTime, 1000);
        updateTime(); // Initial call

        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>
