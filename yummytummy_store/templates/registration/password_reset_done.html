{% extends 'yummytummy_store/base.html' %}
{% load static %}

{% block title %}Password Reset Sent - YummyTummy{% endblock %}

{% block extra_css %}
<style>
    .reset-done-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 20px;
    }
    
    .reset-done-card {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 2px solid var(--light-gray);
        text-align: center;
    }
    
    .success-icon {
        font-size: 4rem;
        color: var(--yellow);
        margin-bottom: 20px;
    }
    
    .reset-done-title {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 15px;
    }
    
    .reset-done-message {
        color: var(--dark-gray);
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 30px;
    }
    
    .info-box {
        background-color: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        text-align: left;
    }
    
    .info-box h4 {
        color: #0056b3;
        margin: 0 0 15px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .info-box ul {
        color: #0056b3;
        margin: 0;
        padding-left: 20px;
    }
    
    .info-box li {
        margin-bottom: 8px;
    }
    
    .back-button {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 15px 30px;
        border: none;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 10px;
    }
    
    .back-button:hover {
        background-color: var(--yellow);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="reset-done-container">
    <div class="reset-done-card">
        <!-- Success Icon -->
        <div class="success-icon">
            <i class="fas fa-envelope-circle-check"></i>
        </div>
        
        <!-- Title -->
        <h1 class="reset-done-title">Check Your Email</h1>
        
        <!-- Message -->
        <p class="reset-done-message">
            We've sent you a password reset link at your email address. 
            Please check your inbox and follow the instructions to reset your password.
        </p>
        
        <!-- Info Box -->
        <div class="info-box">
            <h4>
                <i class="fas fa-info-circle"></i>
                What to do next:
            </h4>
            <ul>
                <li>Check your email inbox (and spam folder)</li>
                <li>Click the password reset link in the email</li>
                <li>Create a new secure password</li>
                <li>Sign in with your new password</li>
            </ul>
        </div>
        
        <!-- Back Button -->
        <a href="{% url 'yummytummy_store:home' %}" class="back-button">
            <i class="fas fa-home"></i>
            Return to Home
        </a>
    </div>
</div>
{% endblock %}
