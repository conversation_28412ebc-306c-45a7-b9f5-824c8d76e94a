{% extends 'yummytummy_store/base.html' %}
{% load static %}

{% block title %}Password Changed - YummyTummy{% endblock %}

{% block extra_css %}
<style>
    .password-done-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 20px;
    }
    
    .password-done-card {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 2px solid var(--light-gray);
        text-align: center;
    }
    
    .success-icon {
        font-size: 4rem;
        color: #28a745;
        margin-bottom: 20px;
    }
    
    .password-done-title {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 15px;
    }
    
    .password-done-message {
        color: var(--dark-gray);
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 30px;
    }
    
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        text-align: left;
    }
    
    .success-box h4 {
        color: #155724;
        margin: 0 0 15px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .success-box ul {
        color: #155724;
        margin: 0;
        padding-left: 20px;
    }
    
    .success-box li {
        margin-bottom: 8px;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn {
        padding: 15px 25px;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
    }
    
    .btn-primary {
        background-color: var(--primary-color);
        color: var(--secondary-color);
    }
    
    .btn-primary:hover {
        background-color: var(--yellow);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .btn-secondary {
        background-color: var(--light-gray);
        color: var(--primary-color);
        border: 2px solid var(--primary-color);
    }
    
    .btn-secondary:hover {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
        }
        
        .btn {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="password-done-container">
    <div class="password-done-card">
        <!-- Success Icon -->
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        
        <!-- Title -->
        <h1 class="password-done-title">Password Changed Successfully!</h1>
        
        <!-- Message -->
        <p class="password-done-message">
            Your password has been updated successfully. Your account is now more secure.
        </p>
        
        <!-- Success Box -->
        <div class="success-box">
            <h4>
                <i class="fas fa-shield-check"></i>
                Security Update Complete
            </h4>
            <ul>
                <li>Your new password is now active</li>
                <li>You'll need to use the new password for future logins</li>
                <li>Your account security has been enhanced</li>
                <li>Consider updating your password manager if you use one</li>
            </ul>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{% url 'yummytummy_store:account_profile' %}" class="btn btn-primary">
                <i class="fas fa-user"></i>
                Back to Profile
            </a>
            <a href="{% url 'yummytummy_store:order_tracking_dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-box"></i>
                View Orders
            </a>
        </div>
    </div>
</div>
{% endblock %}
