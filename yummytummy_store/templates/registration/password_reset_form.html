{% extends 'yummytummy_store/base.html' %}
{% load static %}

{% block title %}Reset Password - YummyTummy{% endblock %}

{% block extra_css %}
<style>
    /* YummyTummy Brand Colors */
    :root {
        --primary-color: #593500;
        --secondary-color: #ffffff;
        --accent-color: #f5f2ed;
        --highlight-color: #ffc107;
        --dark-gray: #666;
        --light-gray: #e0e0e0;
        --yellow: #ffc107;
    }

    .reset-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 20px;
    }

    .reset-card {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 2px solid var(--light-gray);
    }

    .reset-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .reset-logo {
        font-size: 2.5rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .reset-subtitle {
        color: var(--dark-gray);
        font-size: 1.1rem;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 8px;
        font-size: 1rem;
    }
    
    .form-input {
        width: 100%;
        padding: 15px;
        border: 2px solid var(--light-gray);
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--secondary-color);
    }
    
    .form-input:focus {
        outline: none;
        border-color: var(--yellow);
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
    }

    .form-input.error {
        border-color: #dc3545;
    }

    .error-message {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .reset-button {
        width: 100%;
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 15px;
        border: none;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .reset-button:hover {
        background-color: var(--yellow);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .form-links {
        text-align: center;
        margin-top: 20px;
    }
    
    .form-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }
    
    .form-links a:hover {
        color: var(--yellow);
    }
    
    .info-box {
        background-color: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .info-box h4 {
        color: #0056b3;
        margin: 0 0 10px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .info-box p {
        color: #0056b3;
        margin: 0;
        font-size: 0.9rem;
    }

    .additional-info {
        background-color: var(--accent-color);
        border: 1px solid var(--light-gray);
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }

    .additional-info h4 {
        color: var(--primary-color);
        margin: 0 0 10px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .additional-info p {
        color: var(--dark-gray);
        margin: 0;
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .form-links {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .form-links a {
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    @media (max-width: 768px) {
        .reset-container {
            margin: 20px auto;
            padding: 15px;
        }

        .reset-card {
            padding: 30px 20px;
        }

        .reset-logo {
            font-size: 2rem;
        }

        .form-links {
            flex-direction: column;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="reset-container">
    <div class="reset-card">
        <!-- Reset Header -->
        <div class="reset-header">
            <div class="reset-logo">YummyTummy</div>
            <div class="reset-subtitle">Reset your password</div>
        </div>

        <!-- Info Box -->
        <div class="info-box">
            <h4>
                <i class="fas fa-info-circle"></i>
                Password Reset
            </h4>
            <p>
                Enter your email address and we'll send you a link to reset your password.
            </p>
        </div>

        <!-- Reset Form -->
        <form method="post">
            {% csrf_token %}

            <!-- Email Field -->
            <div class="form-group">
                <label for="{{ form.email.id_for_label }}" class="form-label">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                <input type="email"
                       name="{{ form.email.name }}"
                       id="{{ form.email.id_for_label }}"
                       class="form-input {% if form.email.errors %}error{% endif %}"
                       placeholder="Enter your email address"
                       value="{{ form.email.value|default:'' }}"
                       required>
                {% if form.email.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ form.email.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Reset Button -->
            <button type="submit" class="reset-button">
                <i class="fas fa-paper-plane"></i> Send Reset Link
            </button>

            <!-- Form Links -->
            <div class="form-links">
                <a href="{% url 'login' %}">
                    <i class="fas fa-arrow-left"></i> Back to Login
                </a>
                <a href="{% url 'yummytummy_store:home' %}">
                    <i class="fas fa-home"></i> Home
                </a>
            </div>
        </form>

        <!-- Additional Information -->
        <div class="additional-info">
            <h4>
                <i class="fas fa-lightbulb"></i>
                What happens next?
            </h4>
            <p>
                After submitting your email, you'll receive a password reset link within a few minutes.
                Check your spam folder if you don't see it in your inbox. The link will be valid for 24 hours.
            </p>
        </div>
    </div>
</div>
{% endblock %}
