{% extends 'yummytummy_store/base.html' %}
{% load static %}

{% block title %}Change Password - YummyTummy{% endblock %}

{% block extra_css %}
<style>
    .password-change-container {
        max-width: 600px;
        margin: 50px auto;
        padding: 20px;
    }
    
    .back-link {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        color: var(--primary-color);
        text-decoration: none;
        margin-bottom: 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .back-link:hover {
        color: var(--yellow);
        transform: translateX(-5px);
    }
    
    .password-change-card {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 2px solid var(--light-gray);
    }
    
    .password-change-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .password-change-title {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 10px;
    }
    
    .password-change-subtitle {
        color: var(--dark-gray);
        font-size: 1.1rem;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 8px;
        font-size: 1rem;
    }
    
    .form-input {
        width: 100%;
        padding: 15px;
        border: 2px solid var(--light-gray);
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--secondary-color);
    }
    
    .form-input:focus {
        outline: none;
        border-color: var(--yellow);
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
    }
    
    .form-input.error {
        border-color: #dc3545;
    }
    
    .error-message {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .change-button {
        width: 100%;
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 15px;
        border: none;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .change-button:hover {
        background-color: var(--yellow);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .password-requirements {
        background-color: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .password-requirements h4 {
        color: #0056b3;
        margin: 0 0 10px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .password-requirements ul {
        color: #0056b3;
        margin: 0;
        padding-left: 20px;
        font-size: 0.9rem;
    }
    
    .password-requirements li {
        margin-bottom: 5px;
    }
    
    .security-note {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .security-note h4 {
        color: #856404;
        margin: 0 0 10px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .security-note p {
        color: #856404;
        margin: 0;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="password-change-container">
    <!-- Back Link -->
    <a href="{% url 'yummytummy_store:account_profile' %}" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to Profile
    </a>

    <div class="password-change-card">
        <!-- Header -->
        <div class="password-change-header">
            <h1 class="password-change-title">Change Password</h1>
            <div class="password-change-subtitle">Update your account password for better security</div>
        </div>

        <!-- Display Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {% if message.tags == 'success' %}
                        <i class="fas fa-check-circle"></i>
                    {% elif message.tags == 'error' %}
                        <i class="fas fa-exclamation-circle"></i>
                    {% else %}
                        <i class="fas fa-info-circle"></i>
                    {% endif %}
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <!-- Security Note -->
        <div class="security-note">
            <h4>
                <i class="fas fa-shield-alt"></i>
                Security Tip
            </h4>
            <p>
                Choose a strong password that you haven't used elsewhere. 
                A good password is at least 8 characters long and includes a mix of letters, numbers, and symbols.
            </p>
        </div>

        <!-- Password Requirements -->
        <div class="password-requirements">
            <h4>
                <i class="fas fa-list-check"></i>
                Password Requirements
            </h4>
            <ul>
                <li>At least 8 characters long</li>
                <li>Cannot be too similar to your personal information</li>
                <li>Cannot be a commonly used password</li>
                <li>Cannot be entirely numeric</li>
            </ul>
        </div>

        <!-- Change Password Form -->
        <form method="post">
            {% csrf_token %}
            
            <!-- Current Password Field -->
            <div class="form-group">
                <label for="{{ form.old_password.id_for_label }}" class="form-label">
                    <i class="fas fa-key"></i> Current Password
                </label>
                <input type="password" 
                       name="{{ form.old_password.name }}" 
                       id="{{ form.old_password.id_for_label }}"
                       class="form-input {% if form.old_password.errors %}error{% endif %}"
                       placeholder="Enter your current password"
                       required>
                {% if form.old_password.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ form.old_password.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- New Password Field -->
            <div class="form-group">
                <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                    <i class="fas fa-lock"></i> New Password
                </label>
                <input type="password" 
                       name="{{ form.new_password1.name }}" 
                       id="{{ form.new_password1.id_for_label }}"
                       class="form-input {% if form.new_password1.errors %}error{% endif %}"
                       placeholder="Enter your new password"
                       required>
                {% if form.new_password1.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ form.new_password1.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Confirm New Password Field -->
            <div class="form-group">
                <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                    <i class="fas fa-lock"></i> Confirm New Password
                </label>
                <input type="password" 
                       name="{{ form.new_password2.name }}" 
                       id="{{ form.new_password2.id_for_label }}"
                       class="form-input {% if form.new_password2.errors %}error{% endif %}"
                       placeholder="Confirm your new password"
                       required>
                {% if form.new_password2.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ form.new_password2.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Non-field Errors -->
            {% if form.non_field_errors %}
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ form.non_field_errors.0 }}
                </div>
            {% endif %}

            <!-- Change Button -->
            <button type="submit" class="change-button">
                <i class="fas fa-key"></i> Change Password
            </button>
        </form>
    </div>
</div>
{% endblock %}
