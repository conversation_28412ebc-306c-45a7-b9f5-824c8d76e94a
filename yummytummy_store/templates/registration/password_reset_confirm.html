{% extends 'yummytummy_store/base.html' %}
{% load static %}

{% block title %}Set New Password - YummyTummy{% endblock %}

{% block extra_css %}
<style>
    .reset-confirm-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 20px;
    }
    
    .reset-confirm-card {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 2px solid var(--light-gray);
    }
    
    .reset-confirm-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .reset-confirm-logo {
        font-size: 2.5rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 10px;
    }
    
    .reset-confirm-subtitle {
        color: var(--dark-gray);
        font-size: 1.1rem;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 8px;
        font-size: 1rem;
    }
    
    .form-input {
        width: 100%;
        padding: 15px;
        border: 2px solid var(--light-gray);
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--secondary-color);
    }
    
    .form-input:focus {
        outline: none;
        border-color: var(--yellow);
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
    }
    
    .form-input.error {
        border-color: #dc3545;
    }
    
    .error-message {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .reset-button {
        width: 100%;
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 15px;
        border: none;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .reset-button:hover {
        background-color: var(--yellow);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .form-links {
        text-align: center;
        margin-top: 20px;
    }
    
    .form-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }
    
    .form-links a:hover {
        color: var(--yellow);
    }
    
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .alert-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .password-requirements {
        background-color: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .password-requirements h4 {
        color: #0056b3;
        margin: 0 0 10px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .password-requirements ul {
        color: #0056b3;
        margin: 0;
        padding-left: 20px;
        font-size: 0.9rem;
    }
    
    .password-requirements li {
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="reset-confirm-container">
    <div class="reset-confirm-card">
        <!-- Reset Header -->
        <div class="reset-confirm-header">
            <div class="reset-confirm-logo">YummyTummy</div>
            <div class="reset-confirm-subtitle">Set your new password</div>
        </div>

        {% if validlink %}
            <!-- Password Requirements -->
            <div class="password-requirements">
                <h4>
                    <i class="fas fa-shield-alt"></i>
                    Password Requirements
                </h4>
                <ul>
                    <li>At least 8 characters long</li>
                    <li>Cannot be too similar to your personal information</li>
                    <li>Cannot be a commonly used password</li>
                    <li>Cannot be entirely numeric</li>
                </ul>
            </div>

            <!-- Reset Form -->
            <form method="post">
                {% csrf_token %}
                
                <!-- New Password Field -->
                <div class="form-group">
                    <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                        <i class="fas fa-lock"></i> New Password
                    </label>
                    <input type="password" 
                           name="{{ form.new_password1.name }}" 
                           id="{{ form.new_password1.id_for_label }}"
                           class="form-input {% if form.new_password1.errors %}error{% endif %}"
                           placeholder="Enter your new password"
                           required>
                    {% if form.new_password1.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i>
                            {{ form.new_password1.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Confirm Password Field -->
                <div class="form-group">
                    <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                        <i class="fas fa-lock"></i> Confirm New Password
                    </label>
                    <input type="password" 
                           name="{{ form.new_password2.name }}" 
                           id="{{ form.new_password2.id_for_label }}"
                           class="form-input {% if form.new_password2.errors %}error{% endif %}"
                           placeholder="Confirm your new password"
                           required>
                    {% if form.new_password2.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i>
                            {{ form.new_password2.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Non-field Errors -->
                {% if form.non_field_errors %}
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ form.non_field_errors.0 }}
                    </div>
                {% endif %}

                <!-- Reset Button -->
                <button type="submit" class="reset-button">
                    <i class="fas fa-key"></i> Set New Password
                </button>

                <!-- Form Links -->
                <div class="form-links">
                    <a href="{% url 'login' %}">
                        <i class="fas fa-arrow-left"></i> Back to Login
                    </a>
                </div>
            </form>
        {% else %}
            <!-- Invalid Link -->
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                This password reset link is invalid or has expired. Please request a new password reset.
            </div>
            
            <div class="form-links">
                <a href="{% url 'password_reset' %}">
                    <i class="fas fa-redo"></i> Request New Password Reset
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
