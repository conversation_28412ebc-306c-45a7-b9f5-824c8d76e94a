{% extends 'yummytummy_store/base.html' %}
{% load static %}

{% block title %}Password Reset Complete - YummyTummy{% endblock %}

{% block extra_css %}
<style>
    .reset-complete-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 20px;
    }
    
    .reset-complete-card {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 2px solid var(--light-gray);
        text-align: center;
    }
    
    .success-icon {
        font-size: 4rem;
        color: #28a745;
        margin-bottom: 20px;
    }
    
    .reset-complete-title {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 15px;
    }
    
    .reset-complete-message {
        color: var(--dark-gray);
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 30px;
    }
    
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        text-align: left;
    }
    
    .success-box h4 {
        color: #155724;
        margin: 0 0 15px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .success-box p {
        color: #155724;
        margin: 0;
        line-height: 1.5;
    }
    
    .login-button {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 15px 30px;
        border: none;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
    }
    
    .login-button:hover {
        background-color: var(--yellow);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .home-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .home-link:hover {
        color: var(--yellow);
    }
</style>
{% endblock %}

{% block content %}
<div class="reset-complete-container">
    <div class="reset-complete-card">
        <!-- Success Icon -->
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        
        <!-- Title -->
        <h1 class="reset-complete-title">Password Reset Complete!</h1>
        
        <!-- Message -->
        <p class="reset-complete-message">
            Your password has been successfully reset. You can now sign in with your new password.
        </p>
        
        <!-- Success Box -->
        <div class="success-box">
            <h4>
                <i class="fas fa-thumbs-up"></i>
                What's next?
            </h4>
            <p>
                Your account is now secure with your new password. 
                You can sign in and continue enjoying our delicious honey products!
            </p>
        </div>
        
        <!-- Action Buttons -->
        <div>
            <a href="{% url 'login' %}" class="login-button">
                <i class="fas fa-sign-in-alt"></i>
                Sign In Now
            </a>
        </div>
        
        <div>
            <a href="{% url 'yummytummy_store:home' %}" class="home-link">
                <i class="fas fa-home"></i>
                Return to Home
            </a>
        </div>
    </div>
</div>
{% endblock %}
