{% extends 'yummytummy_store/base.html' %}
{% load static %}

{% block title %}Login - YummyTummy{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 20px;
    }
    
    .login-card {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 2px solid var(--light-gray);
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .login-logo {
        font-size: 2.5rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 10px;
    }
    
    .login-subtitle {
        color: var(--dark-gray);
        font-size: 1.1rem;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 8px;
        font-size: 1rem;
    }
    
    .form-input {
        width: 100%;
        padding: 15px;
        border: 2px solid var(--light-gray);
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--secondary-color);
    }
    
    .form-input:focus {
        outline: none;
        border-color: var(--yellow);
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
    }
    
    .form-input.error {
        border-color: #dc3545;
    }
    
    .error-message {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .login-button {
        width: 100%;
        background-color: var(--primary-color);
        color: var(--secondary-color);
        padding: 15px;
        border: none;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .login-button:hover {
        background-color: var(--yellow);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .login-button:active {
        transform: translateY(0);
    }
    
    .form-links {
        text-align: center;
        margin-top: 20px;
    }
    
    .form-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }
    
    .form-links a:hover {
        color: var(--yellow);
    }
    
    .divider {
        text-align: center;
        margin: 30px 0;
        position: relative;
        color: var(--dark-gray);
    }
    
    .divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background-color: var(--light-gray);
        z-index: 1;
    }
    
    .divider span {
        background-color: var(--secondary-color);
        padding: 0 20px;
        position: relative;
        z-index: 2;
    }
    
    .guest-option {
        text-align: center;
        padding: 20px;
        background-color: var(--cream);
        border-radius: 10px;
        border: 2px solid var(--light-gray);
    }
    
    .guest-option h3 {
        color: var(--primary-color);
        margin: 0 0 10px 0;
    }
    
    .guest-option p {
        color: var(--dark-gray);
        margin: 0 0 15px 0;
    }
    
    .guest-button {
        background-color: var(--light-gray);
        color: var(--primary-color);
        padding: 12px 24px;
        border: 2px solid var(--primary-color);
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-block;
    }
    
    .guest-button:hover {
        background-color: var(--primary-color);
        color: var(--secondary-color);
    }
    
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .alert-info {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
    
    @media (max-width: 768px) {
        .login-container {
            margin: 20px auto;
            padding: 15px;
        }
        
        .login-card {
            padding: 30px 20px;
        }
        
        .login-logo {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <!-- Login Header -->
        <div class="login-header">
            <div class="login-logo">YummyTummy</div>
            <div class="login-subtitle">Welcome back! Please sign in to your account.</div>
        </div>

        <!-- Display Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {% if message.tags == 'success' %}
                        <i class="fas fa-check-circle"></i>
                    {% elif message.tags == 'error' %}
                        <i class="fas fa-exclamation-circle"></i>
                    {% else %}
                        <i class="fas fa-info-circle"></i>
                    {% endif %}
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <!-- Login Form -->
        <form method="post">
            {% csrf_token %}
            
            <!-- Username Field -->
            <div class="form-group">
                <label for="{{ form.username.id_for_label }}" class="form-label">
                    <i class="fas fa-user"></i> Email Address
                </label>
                <input type="text" 
                       name="{{ form.username.name }}" 
                       id="{{ form.username.id_for_label }}"
                       class="form-input {% if form.username.errors %}error{% endif %}"
                       placeholder="Enter your email address"
                       value="{{ form.username.value|default:'' }}"
                       required>
                {% if form.username.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ form.username.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Password Field -->
            <div class="form-group">
                <label for="{{ form.password.id_for_label }}" class="form-label">
                    <i class="fas fa-lock"></i> Password
                </label>
                <input type="password" 
                       name="{{ form.password.name }}" 
                       id="{{ form.password.id_for_label }}"
                       class="form-input {% if form.password.errors %}error{% endif %}"
                       placeholder="Enter your password"
                       required>
                {% if form.password.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ form.password.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Non-field Errors -->
            {% if form.non_field_errors %}
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ form.non_field_errors.0 }}
                </div>
            {% endif %}

            <!-- Login Button -->
            <button type="submit" class="login-button">
                <i class="fas fa-sign-in-alt"></i> Sign In
            </button>

            <!-- Form Links -->
            <div class="form-links">
                <a href="{% url 'password_reset' %}">Forgot your password?</a>
            </div>
        </form>

        <!-- Divider -->
        <div class="divider">
            <span>or</span>
        </div>

        <!-- Guest Shopping Option -->
        <div class="guest-option">
            <h3>Continue as Guest</h3>
            <p>You can shop without an account. We'll create one for you during checkout to track your orders.</p>
            <a href="{% url 'yummytummy_store:product_list' %}" class="guest-button">
                <i class="fas fa-shopping-cart"></i> Continue Shopping
            </a>
        </div>
    </div>
</div>
{% endblock %}
