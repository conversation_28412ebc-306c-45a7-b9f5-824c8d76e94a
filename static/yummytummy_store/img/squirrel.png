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
