/* Base Styles */
:root {
    --primary-color: #593500;
    --secondary-color: #ffffff;
    --accent-color: #f5f2ed;
    --highlight-color: #ffc107;
    --text-color: #333333;
    --light-gray: #f7f7f7;
    --medium-gray: #e0e0e0;
    --dark-gray: #666666;
    --yellow: #ffc107;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
}

body {
    font-family: 'Montserrat', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--secondary-color);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

a:hover {
    color: var(--dark-gray);
}

img {
    max-width: 100%;
    height: auto;
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
.site-header {
    position: relative;
    z-index: 100;
    background-color: var(--accent-color);
}

.top-header {
    background-color: var(--accent-color);
    padding: 15px 0;
}

.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-menu, .secondary-menu {
    display: flex;
    gap: 20px;
    align-items: center;
}

.main-menu a, .secondary-menu a {
    font-weight: 600;
    font-size: 0.9rem;
}

.logo img {
    height: 50px;
}

/* Flag icon styling */
.flag-icon {
    height: 16px;
    width: auto;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease;
    vertical-align: middle;
}

.flag-icon:hover {
    transform: scale(1.05);
}

/* Specific styling for navbar flag */
.secondary-menu .flag-icon {
    height: 14px;
    margin: 0 2px;
}

.cart-icon {
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--yellow);
    color: var(--primary-color);
    font-size: 0.75rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
}

/* Mobile Header */
.mobile-header {
    display: none;
    padding: 15px;
    background-color: var(--accent-color);
    position: relative;
}

.mobile-icons {
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-toggle {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.menu-toggle span {
    display: block;
    width: 25px;
    height: 2px;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
}

.mobile-nav {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--accent-color);
    padding: 15px;
    display: none;
    box-shadow: 0 5px 15px rgba(89,53,0,0.1);
    z-index: 100;
}

.mobile-nav.active {
    display: block;
}

.mobile-nav a {
    font-weight: 600;
    display: block;
    padding: 10px 0;
    border-bottom: 1px solid var(--medium-gray);
}

/* Hero Slider */
.hero-slider {
    position: relative;
    width: 100%;
    background-color: #fdf9ed; /* Cream background color */
}

.slider-container {
    position: relative;
    overflow: hidden;
    height: 100vh; /* Full viewport height */
    min-height: 700px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* Background image is now applied directly to the slider-container via inline styles */

.slide {
    position: relative;
    width: 100%;
    max-width: 1400px;
    height: 100%;
    padding: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
}

/* Left content - Brand and variants */
.left-content {
    width: 20%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-right: 20px;
}

.left-content h2 {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 30px;
    line-height: 1;
    text-align: left;
    text-transform: uppercase;
}

.product-variants {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 30px;
}

.variant {
    display: flex;
    align-items: center;
    gap: 10px;
}

.variant img {
    width: 40px;
    height: 40px;
}

.variant-text {
    display: flex;
    flex-direction: column;
}

.variant-text span {
    font-weight: 600;
    font-size: 0.8rem;
    line-height: 1.2;
    text-transform: uppercase;
}

/* Center content - Product image and features */
.center-content {
    width: 50%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.product-image {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.product-image img {
    max-height: 500px;
    max-width: 100%;
}

.features-left {
    position: absolute;
    left: 10%;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 30px;
    z-index: 2;
}

.feature {
    text-align: left;
    color: var(--secondary-color);
}

.percentage {
    font-size: 2.5rem;
    font-weight: 900;
    display: block;
    line-height: 1;
}

.feature p {
    font-weight: 600;
    font-size: 0.9rem;
    line-height: 1.2;
    text-transform: uppercase;
}

/* Right content - Product preview */
.right-content {
    width: 25%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.product-preview {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background-color: var(--secondary-color);
    padding: 25px 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(89,53,0,0.1);
    max-width: 200px;
    position: relative;
}

.squirrel-icon {
    position: absolute;
    top: -20px;
    right: -20px;
    z-index: 2;
}

.squirrel-icon img {
    width: 60px;
}

.feature-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    color: white;
    z-index: 2;
}

.feature-badge.bestseller {
    background-color: #ff6b6b;
}

.feature-badge.seasonal {
    background-color: #51cf66;
}

.feature-badge.limited_time {
    background-color: #fcc419;
}

.feature-badge.new {
    background-color: #339af0;
}

.feature-badge.sale {
    background-color: #f06595;
}

.product-preview h3 {
    font-size: 0.9rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.3;
    text-transform: uppercase;
    text-align: center;
}

.price {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    justify-content: center;
}

.amount {
    font-size: 3rem;
    font-weight: 900;
}

.decimal {
    font-size: 1rem;
    line-height: 1;
    margin-top: 5px;
    font-weight: 700;
}

.quantity-selector {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
    justify-content: center;
}

.quantity-selector button {
    width: 25px;
    height: 25px;
    background-color: var(--secondary-color);
    border: 1px solid var(--medium-gray);
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
}

.quantity-selector input {
    width: 30px;
    height: 25px;
    text-align: center;
    border: none;
    font-size: 1.1rem;
    background-color: transparent;
}

.add-to-cart {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--yellow);
    color: var(--primary-color);
    border-radius: 30px;
    font-weight: 700;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 0.8rem;
    width: 100%;
    text-align: center;
}

.add-to-cart:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

/* About Section */
.about-section {
    padding: 80px 0;
    text-align: center;
}

.section-title {
    font-size: 2.5rem;
    text-transform: lowercase;
    margin-bottom: 10px;
}

.section-subtitle {
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 40px;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: justify;
}

/* Process Section */
.process-section {
    padding: 50px 0;
    background-color: var(--accent-color);
}

.process-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
}

.process-step {
    flex: 1;
    text-align: center;
    padding: 0 20px;
    position: relative;
}

.step-number {
    margin: 20px 0;
}

.process-step h3 {
    font-size: 1.2rem;
    text-transform: uppercase;
    margin-bottom: 20px;
    font-weight: 700;
}

.process-cta {
    text-align: center;
    margin-top: 30px;
}

.btn-primary {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-color);
    color: var(--secondary-color);
    text-transform: lowercase;
    border-radius: 5px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--dark-gray);
    color: var(--secondary-color);
}

/* Testimonials Section */
.testimonials-section {
    padding: 50px 0;
    position: relative;
}

.testimonials-decoration {
    text-align: center;
    margin: 20px 0;
}

.testimonials-slider {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.testimonial {
    display: none;
    text-align: center;
}

.testimonial.active {
    display: block;
}

.testimonial-image {
    margin-bottom: 20px;
}

.testimonial-image img {
    max-width: 150px;
    border-radius: 50%;
}

.testimonial-logo {
    margin-bottom: 20px;
}

.testimonial-content {
    font-style: italic;
    margin-bottom: 20px;
}

.testimonial-author {
    font-weight: 700;
    margin-top: 20px;
}

/* Product Showcase */
.product-showcase {
    padding: 80px 0;
    background-color: var(--accent-color);
    text-align: center;
}

.showcase-title {
    font-size: 1.5rem;
    text-transform: uppercase;
    margin-bottom: 10px;
}

.showcase-subtitle {
    font-size: 2rem;
    text-transform: uppercase;
    margin-bottom: 40px;
    font-weight: 700;
}

.product-slider {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.product-card {
    background-color: var(--secondary-color);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(89,53,0,0.1);
    width: 250px;
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-10px);
}

.product-title {
    font-size: 1rem;
    margin: 15px 0;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-title a {
    color: var(--primary-color);
}

/* Expandable Product Previews Styles */
.expandable-card {
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.expandable-card.expanded {
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(89,53,0,0.2);
    z-index: 10;
}

.card-compact {
    transition: all 0.3s ease;
}

.expandable-card.expanded .card-compact {
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.product-price-summary {
    margin: 15px 0;
}

.price-range-compact {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-weight: 600;
}

.from-label {
    font-size: 0.8rem;
    color: var(--dark-gray);
    font-weight: 500;
}

.range-indicator {
    color: var(--yellow);
    font-weight: 700;
    font-size: 1.2rem;
}

.single-price {
    text-align: center;
    font-weight: 600;
}

.expand-btn {
    background: linear-gradient(135deg, var(--yellow), #f0c14b);
    color: var(--primary-color);
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    margin-top: 10px;
}

.expand-btn:hover {
    background: linear-gradient(135deg, #f0c14b, var(--yellow));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.expand-btn .collapse-text {
    display: none;
}

.expand-btn.expanded .expand-text {
    display: none;
}

.expand-btn.expanded .collapse-text {
    display: inline;
}

.expand-icon {
    transition: transform 0.3s ease;
}

.expand-btn.expanded .expand-icon {
    transform: rotate(180deg);
}

.card-expanded {
    padding: 20px 0;
    border-top: 1px solid var(--light-gray);
    animation: slideDown 0.4s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.variants-header {
    text-align: center;
    margin-bottom: 20px;
}

.variants-header h4 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.variants-subtitle {
    color: var(--dark-gray);
    font-size: 0.9rem;
    margin: 0;
}

.variants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.variant-option {
    transition: transform 0.2s ease;
}

.variant-option:hover {
    transform: translateY(-3px);
}

.variant-card {
    background-color: var(--accent-color);
    border: 2px solid var(--light-gray);
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.variant-card:hover {
    border-color: var(--yellow);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.variant-size {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.variant-price {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.variant-description {
    font-size: 0.8rem;
    color: var(--dark-gray);
    margin-bottom: 15px;
}

.quantity-selector.mini {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
}

.quantity-selector.mini input {
    width: 50px;
    text-align: center;
    padding: 5px;
    border: 1px solid var(--medium-gray);
    border-radius: 4px;
}

.quantity-selector.mini button {
    width: 30px;
    height: 30px;
    border: 1px solid var(--medium-gray);
    background-color: var(--secondary-color);
    color: var(--primary-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quantity-selector.mini button:hover {
    background-color: var(--yellow);
}

.add-to-cart.variant {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.add-to-cart.variant:hover {
    background-color: var(--dark-gray);
    transform: translateY(-1px);
}

.add-to-cart.compact {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 10px;
}

.add-to-cart.compact:hover {
    background-color: var(--dark-gray);
    transform: translateY(-2px);
}

/* Hero Section Expandable Styles */
.highlighted-product.expandable-card {
    position: relative;
    overflow: visible; /* Allow expansion beyond hero bounds */
}

.highlighted-product.expandable-card.expanded {
    transform: none; /* Don't scale in hero section */
    box-shadow: 0 10px 30px rgba(89,53,0,0.2);
    z-index: 20; /* Higher than regular cards */
}

.hero-compact {
    transition: all 0.3s ease;
}

.highlighted-product.expandable-card.expanded .hero-compact {
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.hero-price {
    margin: 15px 0;
}

.hero-range {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-weight: 600;
    font-size: 1.1rem;
}

.hero-range .currency {
    font-size: 1.2rem;
}

.hero-range .amount {
    font-size: 1.4rem;
}

.hero-expand {
    background: linear-gradient(135deg, var(--yellow), #f0c14b);
    color: var(--primary-color);
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    margin-top: 15px;
    font-size: 1rem;
}

.hero-expand:hover {
    background: linear-gradient(135deg, #f0c14b, var(--yellow));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.hero-expanded {
    padding: 20px 0;
    border-top: 1px solid var(--light-gray);
    animation: slideDown 0.4s ease-out;
    background-color: rgba(255,255,255,0.95);
    border-radius: 10px;
    margin-top: 10px;
    box-shadow: 0 5px 20px rgba(89,53,0,0.1);
}

.hero-variants {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.hero-quick-add-form {
    margin-top: 15px;
}

.hero-quick-add-form .quantity-selector {
    margin-bottom: 15px;
}

/* Cart Variant Info Styles */
.variant-info {
    font-size: 0.9rem;
    color: var(--dark-gray);
    margin: 5px 0 0 0;
    font-style: italic;
}

.variant-info::before {
    content: "• ";
    color: var(--yellow);
    font-weight: bold;
}

/* Cart Update Functionality Styles */
.update-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

.update-form .quantity-selector {
    display: flex;
    align-items: center;
    gap: 5px;
    justify-content: center;
}

.update-form .quantity-selector button {
    width: 30px;
    height: 30px;
    border: 1px solid var(--medium-gray);
    background-color: var(--secondary-color);
    color: var(--primary-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
}

.update-form .quantity-selector button:hover {
    background-color: var(--yellow);
    border-color: var(--primary-color);
}

.update-form .quantity-input {
    width: 60px;
    text-align: center;
    padding: 5px;
    border: 1px solid var(--medium-gray);
    border-radius: 4px;
    transition: all 0.2s ease;
}

.update-form .quantity-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(89,53,0,0.1);
}

.update-btn {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.update-btn:hover {
    background-color: var(--dark-gray);
    transform: translateY(-1px);
}

.remove-btn {
    color: #dc3545;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.remove-btn:hover {
    color: #c82333;
    text-decoration: underline;
}

/* Blog Section */
.blog-section {
    padding: 80px 0;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.blog-post {
    border-bottom: 1px solid var(--medium-gray);
    padding-bottom: 20px;
    transition: transform 0.3s ease;
}

.blog-post:hover {
    transform: translateY(-5px);
}

.blog-title {
    font-size: 1.1rem;
    font-weight: 500;
}

.blog-cta {
    text-align: center;
}

.btn-secondary {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--secondary-color);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    text-transform: lowercase;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

/* About Brand Section */
.about-brand {
    padding: 80px 0;
    background-color: var(--accent-color);
    text-align: center;
}

.brand-title {
    font-size: 2rem;
    text-transform: uppercase;
    margin-bottom: 10px;
}

.brand-subtitle {
    font-size: 1.5rem;
    text-transform: lowercase;
    margin-bottom: 40px;
    font-weight: 500;
}

.brand-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: justify;
}

/* Footer */
.site-footer {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: 50px 0 20px;
}

.footer-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.social-links {
    display: flex;
    align-items: center;
    gap: 20px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.contact-info a {
    color: var(--secondary-color);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.2);
    flex-wrap: wrap;
}

.footer-links, .legal-links {
    display: flex;
    gap: 20px;
}

.footer-bottom a {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Kenyan flag in footer */
.kenyan-flag .flag-icon {
    height: 12px;
    vertical-align: middle;
    margin-right: 8px;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .slide {
        max-width: 100%;
        padding: 30px;
    }

    .left-content h2 {
        font-size: 2.2rem;
    }

    .product-image img {
        max-height: 400px;
    }

    .yellow-circle {
        width: 70%;
        padding-bottom: 70%;
    }
}

@media (max-width: 1024px) {
    .slider-container {
        height: auto;
        min-height: 100vh;
        padding: 50px 0;
    }

    .background-image {
        background-size: 85%; /* Slightly larger on smaller screens */
    }

    .slide {
        flex-direction: column;
        height: auto;
        padding: 20px;
        justify-content: flex-start;
    }

    .left-content, .center-content, .right-content {
        width: 100%;
        margin-bottom: 30px;
    }

    .left-content {
        order: 1;
        align-items: center;
        text-align: center;
    }

    .left-content h2 {
        text-align: center;
    }

    .product-variants {
        flex-direction: row;
        justify-content: center;
    }

    .center-content {
        order: 2;
    }

    .features-left {
        position: static;
        flex-direction: row;
        justify-content: center;
        transform: none;
        margin-top: 20px;
        gap: 50px;
    }

    .feature {
        text-align: center;
    }

    .right-content {
        order: 3;
        justify-content: center;
    }

    .product-preview {
        margin: 0 auto;
    }

    .process-steps {
        flex-direction: column;
        gap: 50px;
    }

    .product-slider {
        justify-content: center;
    }

    /* Expandable Cards Responsive */
    .variants-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .variant-card {
        padding: 12px;
    }

    .expand-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    /* Hero Section Responsive */
    .hero-expand {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .hero-variants {
        grid-template-columns: 1fr;
        gap: 10px;
        max-height: 250px;
    }

    .footer-top {
        flex-direction: column;
        gap: 30px;
        align-items: center;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .top-header {
        display: none;
    }

    .mobile-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* Hide flag on mobile since it's not in mobile nav */
    .secondary-menu .flag-icon {
        display: none;
    }

    /* Background image styles are now inline */

    .left-content h2 {
        font-size: 2.2rem;
    }

    .product-variants {
        gap: 20px;
    }

    .variant img {
        width: 35px;
        height: 35px;
    }

    .product-image img {
        max-height: 300px;
    }

    .percentage {
        font-size: 2rem;
    }

    .features-left {
        gap: 30px;
    }

    .blog-grid {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 20px;
        align-items: center;
        text-align: center;
    }

    /* Expandable Cards Mobile */
    .expandable-card.expanded {
        transform: none;
        box-shadow: 0 5px 20px rgba(89,53,0,0.15);
    }

    .variants-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .variant-card {
        padding: 10px;
    }

    .variant-size {
        font-size: 1rem;
    }

    .expand-btn {
        padding: 8px 16px;
        font-size: 0.85rem;
    }

    .variants-header h4 {
        font-size: 1rem;
    }

    .variants-subtitle {
        font-size: 0.8rem;
    }

    /* Hero Section Mobile */
    .hero-expand {
        padding: 8px 16px;
        font-size: 0.85rem;
    }

    .hero-range {
        font-size: 1rem;
    }

    .hero-range .currency {
        font-size: 1.1rem;
    }

    .hero-range .amount {
        font-size: 1.2rem;
    }

    .hero-variants {
        grid-template-columns: 1fr;
        gap: 8px;
        max-height: 200px;
    }

    .hero-expanded {
        padding: 15px 0;
    }

    /* Cart Update Mobile */
    .update-form .quantity-selector {
        gap: 3px;
    }

    .update-form .quantity-selector button {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .update-form .quantity-input {
        width: 50px;
        font-size: 0.9rem;
    }

    .update-btn {
        padding: 5px 10px;
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .left-content h2 {
        font-size: 1.8rem;
    }

    .product-variants {
        flex-direction: column;
    }

    .features-left {
        flex-direction: column;
        gap: 20px;
    }

    .product-preview {
        padding: 20px;
    }

    .amount {
        font-size: 2.5rem;
    }
}

/* Cart Styles */
.cart-section {
    padding: 50px 0;
}

.cart-table {
    width: 100%;
    margin-bottom: 30px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.cart-header {
    display: flex;
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: 15px;
    font-weight: 600;
}

.cart-product {
    flex: 3;
}

.cart-price, .cart-quantity, .cart-subtotal, .cart-actions {
    flex: 1;
    text-align: center;
}

.cart-item {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid var(--light-gray);
    align-items: center;
}

.cart-item:last-child {
    border-bottom: none;
}

.update-form {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.update-btn {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.remove-btn {
    color: #dc3545;
    text-decoration: none;
}

.cart-summary {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Coupon Styles */
.coupon-section {
    width: 100%;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--medium-gray);
}

.coupon-section h3 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.coupon-form {
    margin-bottom: 15px;
}

.coupon-input-group {
    display: flex;
    max-width: 400px;
}

.coupon-input-group input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: 5px 0 0 5px;
    font-size: 0.95rem;
}

.coupon-input-group input:focus {
    outline: none;
    border-color: var(--yellow);
}

.apply-coupon-btn {
    background-color: var(--yellow);
    color: var(--primary-color);
    border: none;
    padding: 10px 20px;
    border-radius: 0 5px 5px 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.apply-coupon-btn:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

.applied-coupon {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px dashed var(--yellow);
    border-radius: 5px;
    padding: 10px 15px;
    margin-top: 15px;
    max-width: 400px;
}

.coupon-info {
    display: flex;
    align-items: center;
}

.coupon-code {
    font-weight: 700;
    color: var(--primary-color);
    margin-right: 10px;
}

.coupon-value {
    background-color: var(--yellow);
    color: var(--primary-color);
    font-size: 0.8rem;
    font-weight: 700;
    padding: 3px 8px;
    border-radius: 3px;
}

.remove-coupon-btn {
    background: none;
    border: none;
    color: var(--dark-gray);
    cursor: pointer;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.remove-coupon-btn:hover {
    color: #dc3545;
}

/* Cart Totals */
.cart-totals {
    width: 100%;
    margin-bottom: 20px;
}

.totals-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--medium-gray);
}

.totals-row.discount {
    color: #28a745;
    font-weight: 600;
}

.totals-row.total {
    font-size: 1.2rem;
    font-weight: 700;
    margin-top: 10px;
    border-bottom: none;
}

.cart-total {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.total-amount {
    color: var(--primary-color);
    margin-left: 10px;
}

.cart-buttons {
    display: flex;
    gap: 20px;
    width: 100%;
    justify-content: space-between;
}

.continue-shopping, .checkout-btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
}

.continue-shopping {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.checkout-btn {
    background-color: var(--yellow);
    color: var(--primary-color);
}

.empty-cart {
    text-align: center;
    padding: 50px 20px;
    background-color: var(--secondary-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.empty-cart-icon {
    color: var(--medium-gray);
    margin-bottom: 20px;
}

.empty-cart p {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.empty-cart-message {
    font-size: 1rem !important;
    color: var(--dark-gray);
    margin-bottom: 30px !important;
}

.empty-cart .continue-shopping {
    display: inline-block;
    margin-top: 20px;
}

/* Messages */
.messages-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    max-width: 400px;
    width: 100%;
}

.message {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideIn 0.3s ease-out;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.message:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}

.message:focus {
    outline: 3px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* Add a subtle left border indicator */
.message::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
    opacity: 0.3;
}

.message.success {
    background-color: #28a745;
    color: var(--secondary-color);
    border-left: 4px solid #1e7e34;
}

.message.info {
    background-color: #17a2b8;
    color: var(--secondary-color);
    border-left: 4px solid #117a8b;
}

.message.warning {
    background-color: var(--highlight-color);
    color: var(--primary-color);
    border-left: 4px solid #e0a800;
}

.message.error {
    background-color: #dc3545;
    color: var(--secondary-color);
    border-left: 4px solid #bd2130;
}

@keyframes slideIn {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.close-message {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: inherit;
    font-size: 1.4rem;
    cursor: pointer;
    padding: 4px 8px;
    margin-left: 15px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 28px;
    font-weight: bold;
    line-height: 1;
}

.close-message:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.close-message:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* Specific close button colors for different message types */
.message.warning .close-message {
    color: var(--primary-color);
    background: rgba(89, 53, 0, 0.2);
}

.message.warning .close-message:hover {
    background: rgba(89, 53, 0, 0.3);
}

/* Mobile Responsive Messages */
@media (max-width: 768px) {
    .messages-container {
        top: 10px;
        left: 10px;
        right: 10px;
        transform: none;
        max-width: none;
    }

    .message {
        padding: 12px 15px;
        font-size: 0.9rem;
        flex-wrap: wrap;
        gap: 8px;
    }

    .close-message {
        font-size: 1.2rem;
        min-width: 24px;
        height: 24px;
        margin-left: 8px;
        padding: 2px 6px;
    }
}

/* Add a subtle pulse animation for new messages */
@keyframes messagePulse {
    0% { box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
    50% { box-shadow: 0 6px 20px rgba(0,0,0,0.25); }
    100% { box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
}

.message {
    animation: slideIn 0.3s ease-out, messagePulse 2s ease-in-out;
}